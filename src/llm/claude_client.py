"""
Anthropic Claude Client

Client implementation for Anthropic's Claude API.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
import anthropic
import time

from .base import (
    BaseLLMClient,
    CompletionRequest,
    ChatRequest,
    CompletionResponse,
    ChatResponse,
    Message,
    MessageRole
)

logger = logging.getLogger(__name__)


class ClaudeClient(BaseLLMClient):
    """
    Anthropic Claude API client implementation.
    """

    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229", **kwargs):
        """
        Initialize Claude client.

        Args:
            api_key: Anthropic API key
            model: Claude model name
            **kwargs: Additional configuration
        """
        super().__init__(api_key, model, **kwargs)

        # Initialize Anthropic client
        self.client = anthropic.AsyncAnthropic(api_key=api_key)

        # Claude-specific settings
        self.max_context_length = self._get_max_context_length(model)

        self.logger.info(f"Initialized Claude client with model: {model}")

    def _get_max_context_length(self, model: str) -> int:
        """Get maximum context length for the model"""
        context_lengths = {
            "claude-3-opus-20240229": 200000,
            "claude-3-sonnet-20240229": 200000,
            "claude-3-haiku-20240307": 200000,
            "claude-2.1": 200000,
            "claude-2.0": 100000,
            "claude-instant-1.2": 100000,
        }
        return context_lengths.get(model, 100000)

    async def complete(self, request: CompletionRequest) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """
        Generate text completion using Claude.

        Args:
            request: Completion request

        Returns:
            CompletionResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)

        start_time = time.time()

        try:
            # Convert completion to chat format (Claude uses messages API)
            messages = [
                {"role": "user", "content": request.prompt}
            ]

            # Prepare parameters
            params = self._prepare_params(request)

            if request.stream:
                return self._stream_completion(messages, params)
            else:
                response = await self.client.messages.create(
                    model=self.model,
                    messages=messages,
                    **params
                )

                response_time = time.time() - start_time

                completion_response = CompletionResponse(
                    text=response.content[0].text,
                    model=self.model,
                    usage={
                        "prompt_tokens": response.usage.input_tokens,
                        "completion_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    },
                    finish_reason=response.stop_reason,
                    response_time=response_time,
                    metadata={"id": response.id}
                )

                self._log_response(completion_response)
                return completion_response

        except Exception as e:
            self.logger.error(f"Completion request failed: {e}")
            raise

    async def chat(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator[Message, None]]:
        """
        Generate chat completion using Claude.

        Args:
            request: Chat request

        Returns:
            ChatResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)

        start_time = time.time()

        try:
            # Convert messages to Claude format
            messages, system_message = self._convert_messages(request.messages)

            # Prepare parameters
            params = self._prepare_params(request)
            if system_message:
                params["system"] = system_message

            if request.stream:
                return self._stream_chat(messages, params)
            else:
                response = await self.client.messages.create(
                    model=self.model,
                    messages=messages,
                    **params
                )

                response_time = time.time() - start_time

                chat_response = ChatResponse(
                    message=Message(
                        role=MessageRole.ASSISTANT,
                        content=response.content[0].text
                    ),
                    model=self.model,
                    usage={
                        "prompt_tokens": response.usage.input_tokens,
                        "completion_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    },
                    finish_reason=response.stop_reason,
                    response_time=response_time,
                    metadata={"id": response.id}
                )

                self._log_response(chat_response)
                return chat_response

        except Exception as e:
            self.logger.error(f"Chat request failed: {e}")
            raise

    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text.

        Note: Claude doesn't provide a direct tokenizer, so we use approximation.

        Args:
            text: Text to count tokens for

        Returns:
            Number of tokens (approximation)
        """
        # Rough approximation: 1 token ≈ 3.5 characters for English
        return int(len(text) / 3.5)

    def validate_request(self, request: Union[CompletionRequest, ChatRequest]) -> bool:
        """
        Validate request parameters for Claude.

        Args:
            request: Request to validate

        Returns:
            True if valid

        Raises:
            ValueError: If request is invalid
        """
        if isinstance(request, CompletionRequest):
            if not request.prompt or not request.prompt.strip():
                raise ValueError("Prompt cannot be empty")

            # Check token limit
            token_count = self.count_tokens(request.prompt)
            if token_count > self.max_context_length:
                raise ValueError(f"Prompt too long: {token_count} tokens (max: {self.max_context_length})")

        elif isinstance(request, ChatRequest):
            if not request.messages:
                raise ValueError("Messages cannot be empty")

            # Check total token count
            total_text = " ".join([msg.content for msg in request.messages])
            token_count = self.count_tokens(total_text)
            if token_count > self.max_context_length:
                raise ValueError(f"Conversation too long: {token_count} tokens (max: {self.max_context_length})")

        return True

    def _prepare_params(self, request: Union[CompletionRequest, ChatRequest]) -> Dict[str, Any]:
        """Prepare parameters for Claude API"""
        params = self._merge_params({
            'max_tokens': request.max_tokens,
            'temperature': request.temperature,
            'top_p': request.top_p,
        })

        claude_params = {
            'max_tokens': params['max_tokens'],
            'temperature': params['temperature'],
            'top_p': params['top_p'],
        }

        if hasattr(request, 'stop') and request.stop:
            claude_params['stop_sequences'] = request.stop

        return claude_params

    def _convert_messages(self, messages: List[Message]) -> tuple[List[Dict[str, str]], Optional[str]]:
        """Convert messages to Claude format"""
        claude_messages = []
        system_message = None

        for message in messages:
            if message.role == MessageRole.SYSTEM:
                system_message = message.content
            else:
                claude_messages.append({
                    "role": message.role.value,
                    "content": message.content
                })

        return claude_messages, system_message

    async def _stream_completion(self, messages: List[Dict[str, str]], params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream completion response"""
        try:
            async with self.client.messages.stream(
                model=self.model,
                messages=messages,
                **params
            ) as stream:
                async for text in stream.text_stream:
                    yield text
        except Exception as e:
            self.logger.error(f"Streaming completion failed: {e}")
            raise

    async def _stream_chat(self, messages: List[Dict[str, str]], params: Dict[str, Any]) -> AsyncGenerator[Message, None]:
        """Stream chat response"""
        try:
            async with self.client.messages.stream(
                model=self.model,
                messages=messages,
                **params
            ) as stream:
                async for text in stream.text_stream:
                    yield Message(
                        role=MessageRole.ASSISTANT,
                        content=text
                    )
        except Exception as e:
            self.logger.error(f"Streaming chat failed: {e}")
            raise