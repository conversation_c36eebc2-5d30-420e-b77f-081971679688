"""
Rate Limiter

Rate limiting utilities for API calls.
"""

import asyncio
import time
import logging
from typing import Dict, Optional
from dataclasses import dataclass
from collections import deque


logger = logging.getLogger(__name__)


@dataclass
class RateLimit:
    """Rate limit configuration"""
    requests_per_minute: int
    tokens_per_minute: int
    requests_per_second: Optional[int] = None
    tokens_per_second: Optional[int] = None


class TokenBucket:
    """
    Token bucket algorithm implementation for rate limiting.
    """

    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket.

        Args:
            capacity: Maximum number of tokens
            refill_rate: Tokens added per second
        """
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self._lock = asyncio.Lock()

    async def consume(self, tokens: int = 1) -> bool:
        """
        Try to consume tokens from bucket.

        Args:
            tokens: Number of tokens to consume

        Returns:
            True if tokens were consumed, False otherwise
        """
        async with self._lock:
            self._refill()

            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False

    async def wait_for_tokens(self, tokens: int = 1) -> float:
        """
        Wait until enough tokens are available.

        Args:
            tokens: Number of tokens needed

        Returns:
            Time waited in seconds
        """
        start_time = time.time()

        while True:
            if await self.consume(tokens):
                return time.time() - start_time

            # Calculate wait time
            async with self._lock:
                self._refill()
                if self.tokens < tokens:
                    needed_tokens = tokens - self.tokens
                    wait_time = needed_tokens / self.refill_rate
                    await asyncio.sleep(min(wait_time, 1.0))  # Max 1 second wait

    def _refill(self):
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill

        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now


class SlidingWindowRateLimiter:
    """
    Sliding window rate limiter implementation.
    """

    def __init__(self, window_size: int, max_requests: int):
        """
        Initialize sliding window rate limiter.

        Args:
            window_size: Window size in seconds
            max_requests: Maximum requests in window
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.requests = deque()
        self._lock = asyncio.Lock()

    async def is_allowed(self) -> bool:
        """
        Check if request is allowed.

        Returns:
            True if request is allowed
        """
        async with self._lock:
            now = time.time()

            # Remove old requests outside window
            while self.requests and self.requests[0] <= now - self.window_size:
                self.requests.popleft()

            # Check if we can add new request
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True

            return False

    async def wait_until_allowed(self) -> float:
        """
        Wait until request is allowed.

        Returns:
            Time waited in seconds
        """
        start_time = time.time()

        while not await self.is_allowed():
            await asyncio.sleep(0.1)

        return time.time() - start_time


class APIRateLimiter:
    """
    Comprehensive rate limiter for API calls with multiple limits.
    """

    def __init__(self, rate_limits: Dict[str, RateLimit]):
        """
        Initialize API rate limiter.

        Args:
            rate_limits: Rate limits per provider/model
        """
        self.rate_limits = rate_limits
        self.request_buckets: Dict[str, TokenBucket] = {}
        self.token_buckets: Dict[str, TokenBucket] = {}
        self.sliding_windows: Dict[str, SlidingWindowRateLimiter] = {}

        # Initialize buckets for each provider
        for provider, limits in rate_limits.items():
            # Request rate limiting
            if limits.requests_per_minute:
                self.request_buckets[provider] = TokenBucket(
                    capacity=limits.requests_per_minute,
                    refill_rate=limits.requests_per_minute / 60.0
                )

            # Token rate limiting
            if limits.tokens_per_minute:
                self.token_buckets[provider] = TokenBucket(
                    capacity=limits.tokens_per_minute,
                    refill_rate=limits.tokens_per_minute / 60.0
                )

            # Per-second limiting
            if limits.requests_per_second:
                self.sliding_windows[provider] = SlidingWindowRateLimiter(
                    window_size=1,
                    max_requests=limits.requests_per_second
                )

        logger.info(f"Initialized rate limiter for {len(rate_limits)} providers")

    async def acquire(self, provider: str, tokens: int = 1) -> float:
        """
        Acquire permission for API call.

        Args:
            provider: Provider name
            tokens: Number of tokens to consume

        Returns:
            Total wait time in seconds
        """
        total_wait = 0.0

        # Check request rate limit
        if provider in self.request_buckets:
            wait_time = await self.request_buckets[provider].wait_for_tokens(1)
            total_wait += wait_time

        # Check token rate limit
        if provider in self.token_buckets:
            wait_time = await self.token_buckets[provider].wait_for_tokens(tokens)
            total_wait += wait_time

        # Check per-second limit
        if provider in self.sliding_windows:
            wait_time = await self.sliding_windows[provider].wait_until_allowed()
            total_wait += wait_time

        if total_wait > 0:
            logger.debug(f"Rate limited {provider}: waited {total_wait:.2f}s")

        return total_wait

    def get_status(self, provider: str) -> Dict[str, float]:
        """
        Get current rate limiter status.

        Args:
            provider: Provider name

        Returns:
            Status information
        """
        status = {}

        if provider in self.request_buckets:
            bucket = self.request_buckets[provider]
            status['request_tokens'] = bucket.tokens
            status['request_capacity'] = bucket.capacity

        if provider in self.token_buckets:
            bucket = self.token_buckets[provider]
            status['token_tokens'] = bucket.tokens
            status['token_capacity'] = bucket.capacity

        return status