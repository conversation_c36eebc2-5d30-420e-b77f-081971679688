# Prompt Templates
# Define reusable prompt templates for different use cases

# Basic completion templates
completion:
  simple:
    template: |
      {prompt}
    description: "Simple completion without any formatting"

  structured:
    template: |
      Task: {task}
      Context: {context}

      Please provide a detailed response:
      {prompt}
    description: "Structured prompt with task and context"

# Chat templates
chat:
  assistant:
    system_message: |
      You are a helpful AI assistant. You provide accurate, helpful, and concise responses.
      Always be polite and professional in your interactions.
    description: "General purpose assistant"

  code_reviewer:
    system_message: |
      You are an expert code reviewer. Analyze the provided code for:
      - Best practices
      - Potential bugs
      - Performance issues
      - Security vulnerabilities
      - Code style and readability

      Provide constructive feedback with specific suggestions for improvement.
    description: "Code review specialist"

  creative_writer:
    system_message: |
      You are a creative writing assistant. Help users with:
      - Story development
      - Character creation
      - Plot structure
      - Writing style improvement

      Be imaginative and provide detailed, engaging content.
    description: "Creative writing helper"

# Few-shot learning templates
few_shot:
  classification:
    template: |
      Classify the following text into one of these categories: {categories}

      Examples:
      {examples}

      Text to classify: {text}
      Category:
    description: "Text classification with examples"

  sentiment_analysis:
    template: |
      Analyze the sentiment of the following text. Respond with: Positive, Negative, or Neutral.

      Examples:
      Text: "I love this product!"
      Sentiment: Positive

      Text: "This is terrible."
      Sentiment: Negative

      Text: "It's okay, nothing special."
      Sentiment: Neutral

      Text: {text}
      Sentiment:
    description: "Sentiment analysis with examples"

# Chain prompting templates
chain:
  research_and_summarize:
    steps:
      - name: "research"
        template: |
          Research the topic: {topic}
          Provide key facts and important information about this topic.
        description: "Research phase"

      - name: "summarize"
        template: |
          Based on the research: {research_results}

          Create a concise summary that includes:
          1. Main points
          2. Key insights
          3. Important conclusions
        description: "Summarization phase"

  analyze_and_recommend:
    steps:
      - name: "analyze"
        template: |
          Analyze the following data/situation: {input}

          Consider:
          - Current state
          - Challenges
          - Opportunities
          - Constraints
        description: "Analysis phase"

      - name: "recommend"
        template: |
          Based on the analysis: {analysis_results}

          Provide specific recommendations:
          1. Immediate actions
          2. Long-term strategies
          3. Success metrics
          4. Potential risks
        description: "Recommendation phase"

# Specialized templates
specialized:
  code_generation:
    template: |
      Generate {language} code for the following requirements:

      Requirements: {requirements}

      Additional constraints:
      {constraints}

      Please provide:
      1. Clean, well-commented code
      2. Error handling
      3. Usage examples
    description: "Code generation with requirements"

  data_analysis:
    template: |
      Analyze the following data: {data}

      Analysis type: {analysis_type}

      Please provide:
      1. Key insights
      2. Patterns and trends
      3. Statistical summary
      4. Recommendations
    description: "Data analysis template"

  translation:
    template: |
      Translate the following text from {source_language} to {target_language}:

      Text: {text}

      Please ensure:
      - Accurate translation
      - Cultural appropriateness
      - Natural flow in target language
    description: "Language translation template"