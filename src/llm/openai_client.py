"""
OpenAI Client

Client implementation for OpenAI's GPT API.
"""

import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
import openai
import tiktoken
import time

from .base import (
    BaseLLMClient,
    CompletionRequest,
    ChatRequest,
    CompletionResponse,
    ChatResponse,
    Message,
    MessageRole
)

logger = logging.getLogger(__name__)


class OpenAIClient(BaseLLMClient):
    """
    OpenAI GPT API client implementation.
    """

    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", **kwargs):
        """
        Initialize OpenAI client.

        Args:
            api_key: OpenAI API key
            model: GPT model name
            **kwargs: Additional configuration
        """
        super().__init__(api_key, model, **kwargs)

        # Initialize OpenAI client
        self.client = openai.AsyncOpenAI(api_key=api_key)

        # Initialize tokenizer
        try:
            self.tokenizer = tiktoken.encoding_for_model(model)
        except KeyError:
            # Fallback to cl100k_base for unknown models
            self.tokenizer = tiktoken.get_encoding("cl100k_base")

        # Model-specific settings
        self.max_context_length = self._get_max_context_length(model)

        self.logger.info(f"Initialized OpenAI client with model: {model}")

    def _get_max_context_length(self, model: str) -> int:
        """Get maximum context length for the model"""
        context_lengths = {
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gpt-4-turbo": 128000,
            "gpt-4-turbo-preview": 128000,
            "gpt-3.5-turbo": 4096,
            "gpt-3.5-turbo-16k": 16384,
            "text-davinci-003": 4097,
            "text-davinci-002": 4097,
        }
        return context_lengths.get(model, 4096)

    async def complete(self, request: CompletionRequest) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """
        Generate text completion using OpenAI.

        Args:
            request: Completion request

        Returns:
            CompletionResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)

        start_time = time.time()

        try:
            # For newer models, use chat completions
            if self.model.startswith(("gpt-4", "gpt-3.5-turbo")):
                return await self._complete_with_chat(request, start_time)
            else:
                return await self._complete_with_completions(request, start_time)

        except Exception as e:
            self.logger.error(f"Completion request failed: {e}")
            raise

    async def chat(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator[Message, None]]:
        """
        Generate chat completion using OpenAI.

        Args:
            request: Chat request

        Returns:
            ChatResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)

        start_time = time.time()

        try:
            # Convert messages to OpenAI format
            messages = self._convert_messages(request.messages)

            # Prepare parameters
            params = self._prepare_params(request)

            if request.stream:
                return self._stream_chat(messages, params)
            else:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    **params
                )

                response_time = time.time() - start_time

                chat_response = ChatResponse(
                    message=Message(
                        role=MessageRole.ASSISTANT,
                        content=response.choices[0].message.content
                    ),
                    model=self.model,
                    usage={
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    finish_reason=response.choices[0].finish_reason,
                    response_time=response_time,
                    metadata={"id": response.id}
                )

                self._log_response(chat_response)
                return chat_response

        except Exception as e:
            self.logger.error(f"Chat request failed: {e}")
            raise

    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text using tiktoken.

        Args:
            text: Text to count tokens for

        Returns:
            Number of tokens
        """
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            self.logger.warning(f"Token counting failed, using approximation: {e}")
            # Fallback: rough approximation
            return len(text) // 4

    def validate_request(self, request: Union[CompletionRequest, ChatRequest]) -> bool:
        """
        Validate request parameters for OpenAI.

        Args:
            request: Request to validate

        Returns:
            True if valid

        Raises:
            ValueError: If request is invalid
        """
        if isinstance(request, CompletionRequest):
            if not request.prompt or not request.prompt.strip():
                raise ValueError("Prompt cannot be empty")

            # Check token limit
            token_count = self.count_tokens(request.prompt)
            if token_count > self.max_context_length:
                raise ValueError(f"Prompt too long: {token_count} tokens (max: {self.max_context_length})")

        elif isinstance(request, ChatRequest):
            if not request.messages:
                raise ValueError("Messages cannot be empty")

            # Check total token count
            total_text = " ".join([msg.content for msg in request.messages])
            token_count = self.count_tokens(total_text)
            if token_count > self.max_context_length:
                raise ValueError(f"Conversation too long: {token_count} tokens (max: {self.max_context_length})")

        return True

    def _prepare_params(self, request: Union[CompletionRequest, ChatRequest]) -> Dict[str, Any]:
        """Prepare parameters for OpenAI API"""
        params = self._merge_params({
            'max_tokens': request.max_tokens,
            'temperature': request.temperature,
            'top_p': request.top_p,
        })

        openai_params = {
            'max_tokens': params['max_tokens'],
            'temperature': params['temperature'],
            'top_p': params['top_p'],
        }

        if hasattr(request, 'stop') and request.stop:
            openai_params['stop'] = request.stop

        return openai_params

    def _convert_messages(self, messages: List[Message]) -> List[Dict[str, str]]:
        """Convert messages to OpenAI format"""
        openai_messages = []

        for message in messages:
            openai_messages.append({
                "role": message.role.value,
                "content": message.content
            })

        return openai_messages

    async def _complete_with_chat(self, request: CompletionRequest, start_time: float) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """Complete using chat completions API"""
        messages = [{"role": "user", "content": request.prompt}]
        params = self._prepare_params(request)

        if request.stream:
            return self._stream_completion_chat(messages, params)
        else:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                **params
            )

            response_time = time.time() - start_time

            return CompletionResponse(
                text=response.choices[0].message.content,
                model=self.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason,
                response_time=response_time,
                metadata={"id": response.id}
            )

    async def _complete_with_completions(self, request: CompletionRequest, start_time: float) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """Complete using legacy completions API"""
        params = self._prepare_params(request)

        if request.stream:
            return self._stream_completion_legacy(request.prompt, params)
        else:
            response = await self.client.completions.create(
                model=self.model,
                prompt=request.prompt,
                **params
            )

            response_time = time.time() - start_time

            return CompletionResponse(
                text=response.choices[0].text,
                model=self.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason,
                response_time=response_time,
                metadata={"id": response.id}
            )

    async def _stream_completion_chat(self, messages: List[Dict[str, str]], params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream completion using chat API"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True,
                **params
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            self.logger.error(f"Streaming completion failed: {e}")
            raise

    async def _stream_completion_legacy(self, prompt: str, params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream completion using legacy API"""
        try:
            stream = await self.client.completions.create(
                model=self.model,
                prompt=prompt,
                stream=True,
                **params
            )

            async for chunk in stream:
                if chunk.choices[0].text:
                    yield chunk.choices[0].text
        except Exception as e:
            self.logger.error(f"Streaming completion failed: {e}")
            raise

    async def _stream_chat(self, messages: List[Dict[str, str]], params: Dict[str, Any]) -> AsyncGenerator[Message, None]:
        """Stream chat response"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True,
                **params
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield Message(
                        role=MessageRole.ASSISTANT,
                        content=chunk.choices[0].delta.content
                    )
        except Exception as e:
            self.logger.error(f"Streaming chat failed: {e}")
            raise