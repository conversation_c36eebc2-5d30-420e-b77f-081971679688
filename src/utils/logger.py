"""
Logger Utilities

Centralized logging configuration and utilities.
"""

import logging
import logging.config
import yaml
import os
from pathlib import Path
from typing import Optional


def setup_logging(config_path: Optional[str] = None, default_level: int = logging.INFO):
    """
    Setup logging configuration.

    Args:
        config_path: Path to logging configuration file
        default_level: Default logging level if config file not found
    """
    if config_path is None:
        config_path = Path(__file__).parent.parent.parent / "config" / "logging_config.yaml"

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Create logs directory if it doesn't exist
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)

            logging.config.dictConfig(config)
            print(f"Logging configured from {config_path}")
        except Exception as e:
            print(f"Error loading logging config: {e}")
            logging.basicConfig(level=default_level)
    else:
        logging.basicConfig(level=default_level)
        print(f"Logging config file not found at {config_path}, using basic config")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class APILogger:
    """
    Specialized logger for API calls with structured logging.
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(f"api.{name}")

    def log_request(self, provider: str, model: str, request_type: str, **kwargs):
        """Log API request"""
        self.logger.info(
            f"API Request",
            extra={
                "provider": provider,
                "model": model,
                "request_type": request_type,
                "metadata": kwargs
            }
        )

    def log_response(self, provider: str, model: str, response_time: float,
                    tokens_used: int, success: bool, **kwargs):
        """Log API response"""
        level = logging.INFO if success else logging.ERROR
        self.logger.log(
            level,
            f"API Response",
            extra={
                "provider": provider,
                "model": model,
                "response_time": response_time,
                "tokens_used": tokens_used,
                "success": success,
                "metadata": kwargs
            }
        )

    def log_error(self, provider: str, model: str, error: str, **kwargs):
        """Log API error"""
        self.logger.error(
            f"API Error",
            extra={
                "provider": provider,
                "model": model,
                "error": error,
                "metadata": kwargs
            }
        )


class PerformanceLogger:
    """
    Logger for performance metrics.
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(f"performance.{name}")

    def log_timing(self, operation: str, duration: float, **kwargs):
        """Log operation timing"""
        self.logger.info(
            f"Performance",
            extra={
                "operation": operation,
                "duration": duration,
                "metadata": kwargs
            }
        )

    def log_cache_hit(self, cache_type: str, key: str):
        """Log cache hit"""
        self.logger.debug(
            f"Cache Hit",
            extra={
                "cache_type": cache_type,
                "key": key
            }
        )

    def log_cache_miss(self, cache_type: str, key: str):
        """Log cache miss"""
        self.logger.debug(
            f"Cache Miss",
            extra={
                "cache_type": cache_type,
                "key": key
            }
        )