#!/usr/bin/env python3
"""
Chat Session Example

Demonstrates interactive chat sessions with different LLM providers.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from llm.openai_client import OpenAIClient
from llm.claude_client import Claude<PERSON><PERSON>
from llm.gemini_client import Gemini<PERSON>lient
from llm.base import ChatRequest, Message, MessageRole
from prompt_engineering.templates import TemplateManager
from utils.logger import setup_logging, get_logger
from handlers.error_handler import ErrorContext


class ChatSession:
    """Interactive chat session manager"""

    def __init__(self, client, system_message: str = None):
        """
        Initialize chat session.

        Args:
            client: LLM client
            system_message: System message for the session
        """
        self.client = client
        self.messages = []
        self.logger = get_logger(f"{__name__}.ChatSession")

        # Add system message if provided
        if system_message:
            self.add_message(MessageRole.SYSTEM, system_message)

    def add_message(self, role: MessageRole, content: str):
        """Add message to conversation"""
        message = Message(role=role, content=content)
        self.messages.append(message)
        self.logger.debug(f"Added {role.value} message: {content[:50]}...")

    async def send_message(self, user_message: str) -> str:
        """
        Send user message and get response.

        Args:
            user_message: User's message

        Returns:
            Assistant's response
        """
        # Add user message
        self.add_message(MessageRole.USER, user_message)

        # Create chat request
        request = ChatRequest(
            messages=self.messages,
            max_tokens=500,
            temperature=0.7
        )

        # Get response
        async with ErrorContext({"session_length": len(self.messages)}) as ctx:
            response = await self.client.chat(request)

            if ctx.error_info:
                return f"Error: {ctx.error_info.message}"

            # Add assistant response to conversation
            assistant_message = response.message.content
            self.add_message(MessageRole.ASSISTANT, assistant_message)

            return assistant_message

    async def stream_message(self, user_message: str):
        """
        Send user message and stream response.

        Args:
            user_message: User's message
        """
        # Add user message
        self.add_message(MessageRole.USER, user_message)

        # Create streaming chat request
        request = ChatRequest(
            messages=self.messages,
            max_tokens=500,
            temperature=0.7,
            stream=True
        )

        # Stream response
        full_response = ""
        async for message_chunk in await self.client.chat(request):
            content = message_chunk.content
            print(content, end="", flush=True)
            full_response += content

        print()  # New line after streaming

        # Add complete response to conversation
        self.add_message(MessageRole.ASSISTANT, full_response)

    def get_conversation_summary(self) -> str:
        """Get summary of conversation"""
        summary = f"Conversation with {len(self.messages)} messages:\n"
        for i, message in enumerate(self.messages):
            role_str = message.role.value.upper()
            content_preview = message.content[:100] + "..." if len(message.content) > 100 else message.content
            summary += f"{i+1}. {role_str}: {content_preview}\n"
        return summary


async def interactive_chat_demo():
    """Interactive chat demonstration"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting interactive chat demo")

    # Choose provider
    providers = {}

    if os.getenv("OPENAI_API_KEY"):
        providers["openai"] = OpenAIClient(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-3.5-turbo"
        )

    if os.getenv("ANTHROPIC_API_KEY"):
        providers["claude"] = ClaudeClient(
            api_key=os.getenv("ANTHROPIC_API_KEY"),
            model="claude-3-haiku-20240307"
        )

    if os.getenv("GOOGLE_API_KEY"):
        providers["gemini"] = GeminiClient(
            api_key=os.getenv("GOOGLE_API_KEY"),
            model="gemini-pro"
        )

    if not providers:
        logger.error("No API keys found. Please set at least one API key.")
        return

    # Let user choose provider
    print("Available providers:")
    for name in providers.keys():
        print(f"- {name}")

    while True:
        provider_choice = input("\nChoose provider (or 'quit' to exit): ").strip().lower()

        if provider_choice == 'quit':
            break

        if provider_choice not in providers:
            print("Invalid provider. Please try again.")
            continue

        client = providers[provider_choice]

        # Load system message from template
        template_manager = TemplateManager()
        system_message = None

        try:
            # Try to use assistant template
            template = template_manager.get_template("chat.assistant")
            if template:
                system_message = template.template
        except:
            system_message = "You are a helpful AI assistant."

        # Start chat session
        session = ChatSession(client, system_message)

        print(f"\nStarting chat with {provider_choice.upper()}. Type 'quit' to exit, 'stream' for streaming mode.")
        print("=" * 50)

        while True:
            user_input = input("\nYou: ").strip()

            if user_input.lower() == 'quit':
                break

            if user_input.lower() == 'stream':
                print("Switching to streaming mode. Type your message:")
                user_input = input("You: ").strip()

                if user_input:
                    print(f"\n{provider_choice.upper()}: ", end="")
                    await session.stream_message(user_input)
                continue

            if user_input:
                print(f"\n{provider_choice.upper()}: ", end="")
                response = await session.send_message(user_input)
                print(response)

        # Show conversation summary
        print("\n" + "=" * 50)
        print("Conversation Summary:")
        print(session.get_conversation_summary())


async def automated_chat_demo():
    """Automated chat demonstration"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting automated chat demo")

    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY not found")
        return

    client = OpenAIClient(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-3.5-turbo"
    )

    # Create chat session with system message
    system_message = "You are a helpful AI assistant that provides concise, informative answers."
    session = ChatSession(client, system_message)

    # Predefined conversation
    questions = [
        "What is machine learning?",
        "How does it differ from traditional programming?",
        "Can you give me a simple example?",
        "What are some common applications?"
    ]

    print("Automated Chat Demo")
    print("=" * 50)

    for question in questions:
        print(f"\nUser: {question}")
        response = await session.send_message(question)
        print(f"Assistant: {response}")

        # Small delay for readability
        await asyncio.sleep(1)

    print("\n" + "=" * 50)
    print("Conversation completed!")
    print(session.get_conversation_summary())


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Chat Session Example")
    parser.add_argument("--mode", choices=["interactive", "automated"],
                       default="interactive", help="Demo mode")

    args = parser.parse_args()

    if args.mode == "interactive":
        asyncio.run(interactive_chat_demo())
    else:
        asyncio.run(automated_chat_demo())