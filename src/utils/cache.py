"""
Cache Utilities

Caching mechanisms for API responses and computed results.
"""

import asyncio
import hashlib
import json
import logging
import pickle
import time
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass
import aiofiles


logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    timestamp: float
    ttl: Optional[float] = None
    access_count: int = 0

    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl

    def touch(self):
        """Update access count and timestamp"""
        self.access_count += 1


class MemoryCache:
    """
    In-memory cache with TTL and LRU eviction.
    """

    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = 3600):
        """
        Initialize memory cache.

        Args:
            max_size: Maximum number of entries
            default_ttl: Default TTL in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()

        logger.info(f"Initialized memory cache with max_size={max_size}, default_ttl={default_ttl}")

    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found/expired
        """
        async with self._lock:
            if key not in self.cache:
                return None

            entry = self.cache[key]

            if entry.is_expired():
                del self.cache[key]
                logger.debug(f"Cache entry expired: {key}")
                return None

            entry.touch()
            logger.debug(f"Cache hit: {key}")
            return entry.data

    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: TTL in seconds (uses default if None)
        """
        async with self._lock:
            if ttl is None:
                ttl = self.default_ttl

            entry = CacheEntry(
                data=value,
                timestamp=time.time(),
                ttl=ttl
            )

            self.cache[key] = entry

            # Evict if over max size
            if len(self.cache) > self.max_size:
                await self._evict_lru()

            logger.debug(f"Cache set: {key}")

    async def delete(self, key: str) -> bool:
        """
        Delete key from cache.

        Args:
            key: Cache key

        Returns:
            True if key was deleted
        """
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                logger.debug(f"Cache delete: {key}")
                return True
            return False

    async def clear(self) -> None:
        """Clear all cache entries"""
        async with self._lock:
            self.cache.clear()
            logger.info("Cache cleared")

    async def cleanup_expired(self) -> int:
        """
        Remove expired entries.

        Returns:
            Number of entries removed
        """
        async with self._lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.is_expired()
            ]

            for key in expired_keys:
                del self.cache[key]

            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired entries")

            return len(expired_keys)

    async def _evict_lru(self) -> None:
        """Evict least recently used entry"""
        if not self.cache:
            return

        # Find entry with lowest access count and oldest timestamp
        lru_key = min(
            self.cache.keys(),
            key=lambda k: (self.cache[k].access_count, self.cache[k].timestamp)
        )

        del self.cache[lru_key]
        logger.debug(f"Evicted LRU entry: {lru_key}")

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "default_ttl": self.default_ttl
        }


class FileCache:
    """
    File-based cache for persistent storage.
    """

    def __init__(self, cache_dir: str = "data/cache", default_ttl: Optional[float] = 3600):
        """
        Initialize file cache.

        Args:
            cache_dir: Directory to store cache files
            default_ttl: Default TTL in seconds
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.default_ttl = default_ttl
        self._lock = asyncio.Lock()

        logger.info(f"Initialized file cache at {cache_dir}")

    def _get_cache_path(self, key: str) -> Path:
        """Get cache file path for key"""
        # Create safe filename from key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"

    async def get(self, key: str) -> Optional[Any]:
        """Get value from file cache"""
        cache_path = self._get_cache_path(key)

        if not cache_path.exists():
            return None

        try:
            async with aiofiles.open(cache_path, 'rb') as f:
                data = await f.read()
                entry = pickle.loads(data)

                if entry.is_expired():
                    await self.delete(key)
                    logger.debug(f"File cache entry expired: {key}")
                    return None

                entry.touch()
                logger.debug(f"File cache hit: {key}")
                return entry.data

        except Exception as e:
            logger.error(f"Error reading cache file {cache_path}: {e}")
            return None

    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in file cache"""
        if ttl is None:
            ttl = self.default_ttl

        entry = CacheEntry(
            data=value,
            timestamp=time.time(),
            ttl=ttl
        )

        cache_path = self._get_cache_path(key)

        try:
            async with aiofiles.open(cache_path, 'wb') as f:
                data = pickle.dumps(entry)
                await f.write(data)

            logger.debug(f"File cache set: {key}")

        except Exception as e:
            logger.error(f"Error writing cache file {cache_path}: {e}")

    async def delete(self, key: str) -> bool:
        """Delete key from file cache"""
        cache_path = self._get_cache_path(key)

        if cache_path.exists():
            try:
                cache_path.unlink()
                logger.debug(f"File cache delete: {key}")
                return True
            except Exception as e:
                logger.error(f"Error deleting cache file {cache_path}: {e}")

        return False

    async def clear(self) -> None:
        """Clear all cache files"""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            logger.info("File cache cleared")
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")


class ResponseCache:
    """
    High-level cache for API responses with automatic key generation.
    """

    def __init__(self, memory_cache: Optional[MemoryCache] = None,
                 file_cache: Optional[FileCache] = None):
        """
        Initialize response cache.

        Args:
            memory_cache: Memory cache instance
            file_cache: File cache instance
        """
        self.memory_cache = memory_cache or MemoryCache()
        self.file_cache = file_cache

        logger.info("Initialized response cache")

    def _generate_key(self, provider: str, model: str, request_data: Dict[str, Any]) -> str:
        """Generate cache key from request parameters"""
        key_data = {
            "provider": provider,
            "model": model,
            "request": request_data
        }

        # Create deterministic hash
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_str.encode()).hexdigest()

    async def get_response(self, provider: str, model: str, request_data: Dict[str, Any]) -> Optional[Any]:
        """Get cached response"""
        key = self._generate_key(provider, model, request_data)

        # Try memory cache first
        result = await self.memory_cache.get(key)
        if result is not None:
            return result

        # Try file cache if available
        if self.file_cache:
            result = await self.file_cache.get(key)
            if result is not None:
                # Store in memory cache for faster access
                await self.memory_cache.set(key, result)
                return result

        return None

    async def set_response(self, provider: str, model: str, request_data: Dict[str, Any],
                          response: Any, ttl: Optional[float] = None) -> None:
        """Cache response"""
        key = self._generate_key(provider, model, request_data)

        # Store in memory cache
        await self.memory_cache.set(key, response, ttl)

        # Store in file cache if available
        if self.file_cache:
            await self.file_cache.set(key, response, ttl)

    async def clear_all(self) -> None:
        """Clear all caches"""
        await self.memory_cache.clear()
        if self.file_cache:
            await self.file_cache.clear()