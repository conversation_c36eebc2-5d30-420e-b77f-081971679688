#!/usr/bin/env python3
"""
Prompt Chaining Example

Demonstrates prompt chaining for complex multi-step tasks.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from llm.openai_client import OpenA<PERSON>lient
from llm.claude_client import Claude<PERSON><PERSON>
from llm.gemini_client import Gemini<PERSON>lient
from llm.base import CompletionRequest
from prompt_engineering.chain import Prompt<PERSON>hain, ChainStep, ChainExecutor, ChainBuilder
from prompt_engineering.templates import TemplateManager
from prompt_engineering.few_shot import FewShotPromptBuilder, Example
from utils.logger import setup_logging, get_logger
from handlers.error_handler import ErrorContext


class LLMChainExecutor(ChainExecutor):
    """Chain executor using LLM clients"""

    def __init__(self, client):
        """
        Initialize executor with LLM client.

        Args:
            client: LLM client to use for execution
        """
        self.client = client
        self.logger = get_logger(f"{__name__}.LLMChainExecutor")

    async def execute_step(self, step, prompt, context):
        """Execute a single chain step using LLM"""
        self.logger.debug(f"Executing step: {step.name}")

        # Create completion request
        request = CompletionRequest(
            prompt=prompt,
            max_tokens=500,
            temperature=0.7
        )

        # Execute with error handling
        async with ErrorContext({"step": step.name, "chain": context.get("_chain_name")}) as ctx:
            response = await self.client.complete(request)

            if ctx.error_info:
                raise Exception(f"Step execution failed: {ctx.error_info.message}")

            return response.text.strip()


async def research_and_summarize_demo():
    """Demonstrate research and summarization chain"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting research and summarization demo")

    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY not found")
        return

    client = OpenAIClient(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-3.5-turbo"
    )

    executor = LLMChainExecutor(client)

    # Create research and summarization chain
    chain = (ChainBuilder("research_and_summarize", "Research a topic and create a summary")
             .step(
                 name="research",
                 template="""Research the topic: {topic}

                 Provide detailed information about:
                 1. Key concepts and definitions
                 2. Important facts and statistics
                 3. Current trends and developments
                 4. Notable examples or case studies

                 Topic: {topic}

                 Research findings:""",
                 output_key="research_results",
                 required_inputs=["topic"]
             )
             .step(
                 name="summarize",
                 template="""Based on the research findings below, create a concise summary.

                 Research findings:
                 {research_results}

                 Create a summary that includes:
                 1. Main points (3-5 key points)
                 2. Key insights
                 3. Important conclusions

                 Summary:""",
                 output_key="summary",
                 required_inputs=["research_results"]
             )
             .build())

    # Execute chain
    topic = "Artificial Intelligence in Healthcare"
    initial_context = {"topic": topic}

    print(f"Research and Summarization Demo")
    print(f"Topic: {topic}")
    print("=" * 60)

    result = await chain.execute(executor, initial_context)

    print(f"\nResearch Results:")
    print("-" * 40)
    print(result.get("research_results", "No research results"))

    print(f"\nSummary:")
    print("-" * 40)
    print(result.get("summary", "No summary"))

    # Show chain execution details
    print(f"\nChain Execution Details:")
    print("-" * 40)
    for i, step_result in enumerate(result.get("_chain_results", [])):
        print(f"Step {i+1}: {step_result.step_name} - {step_result.status.value}")
        if step_result.execution_time:
            print(f"  Execution time: {step_result.execution_time:.2f}s")


async def content_creation_chain_demo():
    """Demonstrate content creation chain"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting content creation chain demo")

    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY not found")
        return

    client = OpenAIClient(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-3.5-turbo"
    )

    executor = LLMChainExecutor(client)

    # Create content creation chain
    chain = PromptChain("content_creation", "Create blog post content")

    # Step 1: Generate outline
    outline_step = ChainStep(
        name="outline",
        template="""Create a detailed outline for a blog post about: {topic}

        The blog post should be informative and engaging for {target_audience}.

        Include:
        1. Compelling title
        2. Introduction hook
        3. Main sections (3-5 sections)
        4. Key points for each section
        5. Conclusion

        Outline:""",
        output_key="outline",
        required_inputs=["topic", "target_audience"]
    )

    # Step 2: Write introduction
    intro_step = ChainStep(
        name="introduction",
        template="""Based on this outline:
        {outline}

        Write an engaging introduction for the blog post. The introduction should:
        1. Hook the reader's attention
        2. Introduce the topic
        3. Preview what the post will cover
        4. Be 2-3 paragraphs long

        Introduction:""",
        output_key="introduction",
        required_inputs=["outline"]
    )

    # Step 3: Write main content
    content_step = ChainStep(
        name="main_content",
        template="""Based on this outline:
        {outline}

        And this introduction:
        {introduction}

        Write the main content sections of the blog post. Follow the outline structure and provide detailed, informative content for each section.

        Main content:""",
        output_key="main_content",
        required_inputs=["outline", "introduction"]
    )

    # Step 4: Write conclusion
    conclusion_step = ChainStep(
        name="conclusion",
        template="""Based on the blog post content:

        Introduction:
        {introduction}

        Main content:
        {main_content}

        Write a strong conclusion that:
        1. Summarizes key points
        2. Provides actionable takeaways
        3. Ends with a call to action

        Conclusion:""",
        output_key="conclusion",
        required_inputs=["introduction", "main_content"]
    )

    # Add steps to chain
    chain.add_step(outline_step)
    chain.add_step(intro_step)
    chain.add_step(content_step)
    chain.add_step(conclusion_step)

    # Execute chain
    initial_context = {
        "topic": "The Future of Remote Work",
        "target_audience": "business professionals and managers"
    }

    print("Content Creation Chain Demo")
    print("=" * 60)

    result = await chain.execute(executor, initial_context)

    # Display results
    sections = ["outline", "introduction", "main_content", "conclusion"]

    for section in sections:
        if section in result:
            print(f"\n{section.upper().replace('_', ' ')}:")
            print("-" * 40)
            print(result[section])

    # Show execution summary
    print(f"\nExecution Summary:")
    print("-" * 40)
    total_time = sum(r.execution_time or 0 for r in result.get("_chain_results", []))
    print(f"Total execution time: {total_time:.2f}s")
    print(f"Steps completed: {len([r for r in result.get('_chain_results', []) if r.status.value == 'completed'])}")


async def conditional_chain_demo():
    """Demonstrate conditional chain execution"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting conditional chain demo")

    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY not found")
        return

    client = OpenAIClient(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-3.5-turbo"
    )

    executor = LLMChainExecutor(client)

    # Create conditional chain
    def needs_translation(context):
        """Check if translation is needed"""
        return context.get("language", "english").lower() != "english"

    def needs_simplification(context):
        """Check if simplification is needed"""
        return context.get("complexity", "normal").lower() == "simple"

    chain = (ChainBuilder("conditional_processing", "Process text with conditions")
             .step(
                 name="analyze",
                 template="""Analyze this text: {text}

                 Provide:
                 1. Main topic
                 2. Complexity level (simple/normal/complex)
                 3. Key themes

                 Analysis:""",
                 output_key="analysis",
                 required_inputs=["text"]
             )
             .conditional_step(
                 name="translate",
                 template="""Translate this text to {language}:
                 {text}

                 Translation:""",
                 condition=needs_translation,
                 output_key="translated_text",
                 required_inputs=["text", "language"]
             )
             .conditional_step(
                 name="simplify",
                 template="""Simplify this text for easier understanding:
                 {text}

                 Use simple words and shorter sentences.

                 Simplified text:""",
                 condition=needs_simplification,
                 output_key="simplified_text",
                 required_inputs=["text"]
             )
             .build())

    # Test different scenarios
    scenarios = [
        {
            "text": "Quantum computing leverages quantum mechanical phenomena to process information.",
            "language": "english",
            "complexity": "normal"
        },
        {
            "text": "Machine learning algorithms can identify patterns in large datasets.",
            "language": "spanish",
            "complexity": "normal"
        },
        {
            "text": "Artificial intelligence systems use computational methods to simulate human intelligence.",
            "language": "english",
            "complexity": "simple"
        }
    ]

    print("Conditional Chain Demo")
    print("=" * 60)

    for i, scenario in enumerate(scenarios, 1):
        print(f"\nScenario {i}:")
        print(f"Language: {scenario['language']}")
        print(f"Complexity: {scenario['complexity']}")
        print("-" * 40)

        result = await chain.execute(executor, scenario)

        print(f"Analysis: {result.get('analysis', 'N/A')}")

        if 'translated_text' in result:
            print(f"Translation: {result['translated_text']}")

        if 'simplified_text' in result:
            print(f"Simplified: {result['simplified_text']}")

        # Show which steps were executed
        executed_steps = [r.step_name for r in result.get("_chain_results", [])
                         if r.status.value == "completed"]
        print(f"Executed steps: {', '.join(executed_steps)}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Prompt Chaining Examples")
    parser.add_argument("--demo", choices=["research", "content", "conditional", "all"],
                       default="all", help="Demo to run")

    args = parser.parse_args()

    async def run_demos():
        if args.demo in ["research", "all"]:
            await research_and_summarize_demo()
            print("\n" + "="*80 + "\n")

        if args.demo in ["content", "all"]:
            await content_creation_chain_demo()
            print("\n" + "="*80 + "\n")

        if args.demo in ["conditional", "all"]:
            await conditional_chain_demo()

    asyncio.run(run_demos())