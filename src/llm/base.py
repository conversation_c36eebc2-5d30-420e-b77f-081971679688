"""
Base LLM Client

Abstract base class for all LLM providers with common interface and functionality.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from dataclasses import dataclass
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class MessageRole(Enum):
    """Message roles for chat conversations"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


@dataclass
class Message:
    """Represents a chat message"""
    role: MessageRole
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class CompletionRequest:
    """Request for text completion"""
    prompt: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stop: Optional[List[str]] = None
    stream: bool = False
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ChatRequest:
    """Request for chat completion"""
    messages: List[Message]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stop: Optional[List[str]] = None
    stream: bool = False
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class CompletionResponse:
    """Response from completion request"""
    text: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    metadata: Optional[Dict[str, Any]] = None
    response_time: Optional[float] = None


@dataclass
class ChatResponse:
    """Response from chat request"""
    message: Message
    model: str
    usage: Dict[str, int]
    finish_reason: str
    metadata: Optional[Dict[str, Any]] = None
    response_time: Optional[float] = None


class BaseLLMClient(ABC):
    """
    Abstract base class for LLM clients.

    Provides common interface and functionality for all LLM providers.
    """

    def __init__(self, api_key: str, model: str, **kwargs):
        """
        Initialize the LLM client.

        Args:
            api_key: API key for the provider
            model: Model name to use
            **kwargs: Additional configuration options
        """
        self.api_key = api_key
        self.model = model
        self.config = kwargs
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Default parameters
        self.default_max_tokens = kwargs.get('max_tokens', 1000)
        self.default_temperature = kwargs.get('temperature', 0.7)
        self.default_top_p = kwargs.get('top_p', 1.0)

        # Rate limiting and retry settings
        self.max_retries = kwargs.get('max_retries', 3)
        self.timeout = kwargs.get('timeout', 30)

        self.logger.info(f"Initialized {self.__class__.__name__} with model: {model}")

    @abstractmethod
    async def complete(self, request: CompletionRequest) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """
        Generate text completion.

        Args:
            request: Completion request parameters

        Returns:
            CompletionResponse or AsyncGenerator for streaming
        """
        pass

    @abstractmethod
    async def chat(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator[Message, None]]:
        """
        Generate chat completion.

        Args:
            request: Chat request parameters

        Returns:
            ChatResponse or AsyncGenerator for streaming
        """
        pass

    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text.

        Args:
            text: Text to count tokens for

        Returns:
            Number of tokens
        """
        pass

    @abstractmethod
    def validate_request(self, request: Union[CompletionRequest, ChatRequest]) -> bool:
        """
        Validate request parameters.

        Args:
            request: Request to validate

        Returns:
            True if valid, raises exception if invalid
        """
        pass

    def _merge_params(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge request parameters with defaults.

        Args:
            request_params: Parameters from request

        Returns:
            Merged parameters
        """
        params = {
            'max_tokens': self.default_max_tokens,
            'temperature': self.default_temperature,
            'top_p': self.default_top_p,
        }

        # Override with request-specific parameters
        for key, value in request_params.items():
            if value is not None:
                params[key] = value

        return params

    def _log_request(self, request: Union[CompletionRequest, ChatRequest]):
        """Log request details"""
        request_type = "completion" if isinstance(request, CompletionRequest) else "chat"
        self.logger.debug(f"Making {request_type} request to {self.model}")

        if hasattr(request, 'prompt'):
            self.logger.debug(f"Prompt length: {len(request.prompt)} characters")
        elif hasattr(request, 'messages'):
            self.logger.debug(f"Messages count: {len(request.messages)}")

    def _log_response(self, response: Union[CompletionResponse, ChatResponse]):
        """Log response details"""
        self.logger.debug(f"Response received in {response.response_time:.2f}s")
        self.logger.debug(f"Usage: {response.usage}")
        self.logger.debug(f"Finish reason: {response.finish_reason}")

    async def health_check(self) -> bool:
        """
        Check if the client is healthy and can make requests.

        Returns:
            True if healthy, False otherwise
        """
        try:
            # Simple test request
            test_request = CompletionRequest(
                prompt="Test",
                max_tokens=1,
                temperature=0
            )
            await self.complete(test_request)
            return True
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False