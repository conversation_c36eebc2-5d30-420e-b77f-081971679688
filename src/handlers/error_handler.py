"""
Error Handling

Robust error handling system for LLM operations.
"""

import logging
import traceback
from typing import Dict, List, Optional, Any, Type, Callable
from dataclasses import dataclass
from enum import Enum
import asyncio
from functools import wraps
import time


logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """Types of errors that can occur"""
    API_ERROR = "api_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHENTICATION_ERROR = "authentication_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    NETWORK_ERROR = "network_error"
    QUOTA_EXCEEDED = "quota_exceeded"
    MODEL_ERROR = "model_error"
    TEMPLATE_ERROR = "template_error"
    CACHE_ERROR = "cache_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Detailed error information"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[float] = None
    traceback: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class LLMError(Exception):
    """Base exception for LLM operations"""

    def __init__(self, message: str, error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_info = ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=message,
            details=details,
            traceback=traceback.format_exc()
        )


class APIError(LLMError):
    """API-related errors"""

    def __init__(self, message: str, status_code: Optional[int] = None,
                 response_data: Optional[Dict[str, Any]] = None):
        details = {"status_code": status_code, "response_data": response_data}
        super().__init__(message, ErrorType.API_ERROR, ErrorSeverity.HIGH, details)


class RateLimitError(LLMError):
    """Rate limiting errors"""

    def __init__(self, message: str, retry_after: Optional[float] = None):
        details = {"retry_after": retry_after}
        super().__init__(message, ErrorType.RATE_LIMIT_ERROR, ErrorSeverity.MEDIUM, details)


class AuthenticationError(LLMError):
    """Authentication errors"""

    def __init__(self, message: str):
        super().__init__(message, ErrorType.AUTHENTICATION_ERROR, ErrorSeverity.CRITICAL)


class ValidationError(LLMError):
    """Validation errors"""

    def __init__(self, message: str, field: Optional[str] = None):
        details = {"field": field} if field else None
        super().__init__(message, ErrorType.VALIDATION_ERROR, ErrorSeverity.LOW, details)


class TimeoutError(LLMError):
    """Timeout errors"""

    def __init__(self, message: str, timeout_duration: Optional[float] = None):
        details = {"timeout_duration": timeout_duration}
        super().__init__(message, ErrorType.TIMEOUT_ERROR, ErrorSeverity.MEDIUM, details)


class QuotaExceededError(LLMError):
    """Quota exceeded errors"""

    def __init__(self, message: str, quota_type: Optional[str] = None):
        details = {"quota_type": quota_type}
        super().__init__(message, ErrorType.QUOTA_EXCEEDED, ErrorSeverity.HIGH, details)


class ErrorHandler:
    """
    Centralized error handling system.
    """

    def __init__(self):
        """Initialize error handler"""
        self.error_callbacks: Dict[ErrorType, List[Callable]] = {}
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def register_callback(self, error_type: ErrorType, callback: Callable[[ErrorInfo], None]) -> None:
        """
        Register callback for specific error type.

        Args:
            error_type: Type of error to handle
            callback: Callback function
        """
        if error_type not in self.error_callbacks:
            self.error_callbacks[error_type] = []

        self.error_callbacks[error_type].append(callback)
        self.logger.debug(f"Registered callback for {error_type.value}")

    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """
        Handle an error and create error info.

        Args:
            error: Exception to handle
            context: Additional context information

        Returns:
            Error information
        """
        # Create error info
        if isinstance(error, LLMError):
            error_info = error.error_info
            if context:
                error_info.context = context
        else:
            error_info = ErrorInfo(
                error_type=self._classify_error(error),
                severity=self._determine_severity(error),
                message=str(error),
                traceback=traceback.format_exc(),
                context=context
            )

        # Log error
        self._log_error(error_info)

        # Store in history
        self._store_error(error_info)

        # Execute callbacks
        self._execute_callbacks(error_info)

        return error_info

    def _classify_error(self, error: Exception) -> ErrorType:
        """Classify error type based on exception"""
        error_name = error.__class__.__name__.lower()

        if "timeout" in error_name:
            return ErrorType.TIMEOUT_ERROR
        elif "network" in error_name or "connection" in error_name:
            return ErrorType.NETWORK_ERROR
        elif "auth" in error_name or "permission" in error_name:
            return ErrorType.AUTHENTICATION_ERROR
        elif "rate" in error_name or "limit" in error_name:
            return ErrorType.RATE_LIMIT_ERROR
        elif "validation" in error_name or "value" in error_name:
            return ErrorType.VALIDATION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    def _determine_severity(self, error: Exception) -> ErrorSeverity:
        """Determine error severity"""
        error_name = error.__class__.__name__.lower()

        if "critical" in error_name or "fatal" in error_name:
            return ErrorSeverity.CRITICAL
        elif "auth" in error_name or "permission" in error_name:
            return ErrorSeverity.CRITICAL
        elif "timeout" in error_name or "network" in error_name:
            return ErrorSeverity.HIGH
        elif "rate" in error_name or "limit" in error_name:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW

    def _log_error(self, error_info: ErrorInfo) -> None:
        """Log error based on severity"""
        log_message = f"{error_info.error_type.value}: {error_info.message}"

        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, extra={"error_info": error_info})
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra={"error_info": error_info})
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message, extra={"error_info": error_info})
        else:
            self.logger.info(log_message, extra={"error_info": error_info})

    def _store_error(self, error_info: ErrorInfo) -> None:
        """Store error in history"""
        self.error_history.append(error_info)

        # Trim history if too large
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]

    def _execute_callbacks(self, error_info: ErrorInfo) -> None:
        """Execute registered callbacks for error type"""
        callbacks = self.error_callbacks.get(error_info.error_type, [])

        for callback in callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(f"Error in callback execution: {e}")

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        if not self.error_history:
            return {"total_errors": 0}

        # Count by type
        type_counts = {}
        severity_counts = {}

        for error_info in self.error_history:
            error_type = error_info.error_type.value
            severity = error_info.severity.value

            type_counts[error_type] = type_counts.get(error_type, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        return {
            "total_errors": len(self.error_history),
            "by_type": type_counts,
            "by_severity": severity_counts,
            "recent_errors": len([e for e in self.error_history if time.time() - e.timestamp < 3600])
        }

    def clear_history(self) -> None:
        """Clear error history"""
        self.error_history.clear()
        self.logger.info("Cleared error history")


# Global error handler instance
global_error_handler = ErrorHandler()


def handle_llm_errors(max_retries: int = 3, backoff_factor: float = 1.0,
                     retry_on: Optional[List[Type[Exception]]] = None):
    """
    Decorator for handling LLM errors with retry logic.

    Args:
        max_retries: Maximum number of retries
        backoff_factor: Backoff factor for exponential backoff
        retry_on: List of exception types to retry on
    """
    if retry_on is None:
        retry_on = [RateLimitError, TimeoutError, APIError]

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_error = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_error = e

                    # Handle error
                    error_info = global_error_handler.handle_error(e, {
                        "function": func.__name__,
                        "attempt": attempt + 1,
                        "max_retries": max_retries
                    })

                    # Check if we should retry
                    should_retry = (
                        attempt < max_retries and
                        any(isinstance(e, retry_type) for retry_type in retry_on)
                    )

                    if not should_retry:
                        break

                    # Calculate backoff time
                    backoff_time = backoff_factor * (2 ** attempt)

                    # Special handling for rate limit errors
                    if isinstance(e, RateLimitError) and e.error_info.details:
                        retry_after = e.error_info.details.get("retry_after")
                        if retry_after:
                            backoff_time = max(backoff_time, retry_after)

                    logger.info(f"Retrying {func.__name__} in {backoff_time:.2f}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(backoff_time)

            # All retries exhausted
            raise last_error

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_error = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_error = e

                    # Handle error
                    error_info = global_error_handler.handle_error(e, {
                        "function": func.__name__,
                        "attempt": attempt + 1,
                        "max_retries": max_retries
                    })

                    # Check if we should retry
                    should_retry = (
                        attempt < max_retries and
                        any(isinstance(e, retry_type) for retry_type in retry_on)
                    )

                    if not should_retry:
                        break

                    # Calculate backoff time
                    backoff_time = backoff_factor * (2 ** attempt)

                    # Special handling for rate limit errors
                    if isinstance(e, RateLimitError) and e.error_info.details:
                        retry_after = e.error_info.details.get("retry_after")
                        if retry_after:
                            backoff_time = max(backoff_time, retry_after)

                    logger.info(f"Retrying {func.__name__} in {backoff_time:.2f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(backoff_time)

            # All retries exhausted
            raise last_error

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def safe_execute(func: Callable, *args, default_return=None, **kwargs):
    """
    Safely execute a function with error handling.

    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Default return value on error
        **kwargs: Function keyword arguments

    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        global_error_handler.handle_error(e, {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "args": args,
            "kwargs": kwargs
        })
        return default_return


async def safe_execute_async(func: Callable, *args, default_return=None, **kwargs):
    """
    Safely execute an async function with error handling.

    Args:
        func: Async function to execute
        *args: Function arguments
        default_return: Default return value on error
        **kwargs: Function keyword arguments

    Returns:
        Function result or default_return on error
    """
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        global_error_handler.handle_error(e, {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "args": args,
            "kwargs": kwargs
        })
        return default_return


class ErrorContext:
    """
    Context manager for error handling.
    """

    def __init__(self, context_info: Optional[Dict[str, Any]] = None,
                 suppress_errors: bool = False):
        """
        Initialize error context.

        Args:
            context_info: Additional context information
            suppress_errors: Whether to suppress errors
        """
        self.context_info = context_info or {}
        self.suppress_errors = suppress_errors
        self.error_info: Optional[ErrorInfo] = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_val:
            self.error_info = global_error_handler.handle_error(exc_val, self.context_info)
            return self.suppress_errors
        return False

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_val:
            self.error_info = global_error_handler.handle_error(exc_val, self.context_info)
            return self.suppress_errors
        return False