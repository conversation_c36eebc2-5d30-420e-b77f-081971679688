#!/usr/bin/env python3
"""
Advanced Cache Demo

Demonstrates all cache features and strategies.
"""

import asyncio
import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.cache import MemoryCache, FileCache, ResponseCache, CacheEntry
from utils.logger import setup_logging, get_logger


async def demo_memory_cache():
    """Demo memory cache features"""
    print("🧠 Memory Cache Demo")
    print("-" * 40)
    
    # Initialize with custom settings
    cache = MemoryCache(max_size=5, default_ttl=10)  # Small size for demo
    
    # Test basic operations
    print("1. Basic Operations:")
    await cache.set("key1", "value1", ttl=5)
    await cache.set("key2", {"complex": "data", "numbers": [1, 2, 3]})
    await cache.set("key3", "value3")
    
    # Get values
    val1 = await cache.get("key1")
    val2 = await cache.get("key2")
    print(f"   key1: {val1}")
    print(f"   key2: {val2}")
    
    # Test TTL expiration
    print("\n2. TTL Expiration Test:")
    print("   Waiting 6 seconds for key1 to expire...")
    await asyncio.sleep(6)
    
    expired_val = await cache.get("key1")
    print(f"   key1 after expiration: {expired_val}")
    
    # Test LRU eviction
    print("\n3. LRU Eviction Test:")
    print("   Adding more items to trigger eviction...")
    
    for i in range(4, 10):  # Will exceed max_size=5
        await cache.set(f"key{i}", f"value{i}")
        stats = cache.get_stats()
        print(f"   Added key{i}, cache size: {stats['size']}")
    
    # Check what survived
    print("\n   Surviving keys:")
    for i in range(1, 10):
        val = await cache.get(f"key{i}")
        if val:
            print(f"   key{i}: {val}")
    
    # Cleanup test
    print("\n4. Cleanup Test:")
    await cache.set("temp1", "data1", ttl=2)
    await cache.set("temp2", "data2", ttl=2)
    await cache.set("temp3", "data3", ttl=10)
    
    print("   Added 3 items with different TTLs")
    await asyncio.sleep(3)
    
    expired_count = await cache.cleanup_expired()
    print(f"   Cleaned up {expired_count} expired entries")
    
    stats = cache.get_stats()
    print(f"   Final cache stats: {stats}")


async def demo_file_cache():
    """Demo file cache features"""
    print("\n💾 File Cache Demo")
    print("-" * 40)
    
    # Initialize file cache
    file_cache = FileCache(cache_dir="demo_cache", default_ttl=30)
    
    # Test complex data persistence
    print("1. Complex Data Persistence:")
    complex_data = {
        "user_profile": {
            "name": "John Doe",
            "preferences": ["AI", "Python", "Cache"],
            "settings": {"theme": "dark", "notifications": True}
        },
        "session_data": {
            "login_time": time.time(),
            "actions": ["login", "browse", "search"]
        },
        "metadata": {
            "version": "1.0",
            "created": time.time()
        }
    }
    
    await file_cache.set("user_session_123", complex_data)
    print("   Saved complex user session data")
    
    # Retrieve data
    retrieved = await file_cache.get("user_session_123")
    print(f"   Retrieved: {retrieved['user_profile']['name']}")
    
    # Test persistence across "restarts"
    print("\n2. Persistence Test:")
    await file_cache.set("persistent_key", "This survives restarts", ttl=3600)
    
    # Create new cache instance (simulates restart)
    new_cache = FileCache(cache_dir="demo_cache")
    persistent_data = await new_cache.get("persistent_key")
    print(f"   Data after 'restart': {persistent_data}")
    
    # Cleanup
    await file_cache.clear()
    print("   Cleaned up demo cache files")


async def demo_response_cache():
    """Demo response cache with multi-level caching"""
    print("\n🎯 Response Cache Demo")
    print("-" * 40)
    
    # Initialize multi-level cache
    memory_cache = MemoryCache(max_size=100, default_ttl=300)  # 5 minutes
    file_cache = FileCache(cache_dir="response_cache", default_ttl=3600)  # 1 hour
    response_cache = ResponseCache(memory_cache, file_cache)
    
    # Simulate API requests
    print("1. Multi-Level Caching:")
    
    request_data = {
        "prompt": "What is machine learning?",
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    # First request (cache miss)
    print("   First request (cache miss)...")
    start_time = time.time()
    
    cached = await response_cache.get_response("openai", "gpt-3.5-turbo", request_data)
    if cached is None:
        # Simulate API call
        await asyncio.sleep(1)  # Simulate API latency
        api_response = "Machine learning is a subset of artificial intelligence..."
        
        # Cache the response
        await response_cache.set_response(
            "openai", "gpt-3.5-turbo", 
            request_data, api_response, 
            ttl=1800
        )
        result = api_response
    else:
        result = cached
    
    first_time = time.time() - start_time
    print(f"   Response: {result[:50]}...")
    print(f"   Time: {first_time:.3f}s")
    
    # Second request (memory cache hit)
    print("\n   Second request (memory cache hit)...")
    start_time = time.time()
    
    cached = await response_cache.get_response("openai", "gpt-3.5-turbo", request_data)
    second_time = time.time() - start_time
    
    print(f"   Response: {cached[:50]}...")
    print(f"   Time: {second_time:.3f}s")
    print(f"   Speedup: {first_time/second_time:.1f}x faster")
    
    # Clear memory cache, test file cache
    print("\n   Clearing memory cache, testing file cache...")
    await memory_cache.clear()
    
    start_time = time.time()
    cached = await response_cache.get_response("openai", "gpt-3.5-turbo", request_data)
    third_time = time.time() - start_time
    
    print(f"   File cache hit time: {third_time:.3f}s")
    print(f"   File cache speedup: {first_time/third_time:.1f}x faster than API")
    
    # Test different providers/models
    print("\n2. Provider-Specific Caching:")
    
    providers = [
        ("openai", "gpt-4"),
        ("anthropic", "claude-3"),
        ("google", "gemini-pro")
    ]
    
    for provider, model in providers:
        key = f"{provider}_{model}_test"
        await response_cache.set_response(
            provider, model, 
            {"prompt": "Hello"}, 
            f"Response from {provider} {model}",
            ttl=600
        )
        
        cached = await response_cache.get_response(provider, model, {"prompt": "Hello"})
        print(f"   {provider} {model}: {cached}")
    
    # Cleanup
    await response_cache.clear_all()
    print("\n   Cleaned up all caches")


async def demo_cache_strategies():
    """Demo different caching strategies"""
    print("\n🎛️ Cache Strategies Demo")
    print("-" * 40)
    
    # Strategy 1: Short-term memory cache for frequent requests
    frequent_cache = MemoryCache(max_size=50, default_ttl=60)  # 1 minute
    
    # Strategy 2: Long-term file cache for expensive computations
    expensive_cache = FileCache(cache_dir="expensive_cache", default_ttl=86400)  # 24 hours
    
    # Strategy 3: Session cache for user data
    session_cache = MemoryCache(max_size=1000, default_ttl=1800)  # 30 minutes
    
    print("1. Frequent Requests Cache (1 minute TTL):")
    await frequent_cache.set("weather_api", "Sunny, 25°C")
    weather = await frequent_cache.get("weather_api")
    print(f"   Weather: {weather}")
    
    print("\n2. Expensive Computation Cache (24 hour TTL):")
    computation_result = {"model_training": "completed", "accuracy": 0.95, "time": "2 hours"}
    await expensive_cache.set("ml_model_v1", computation_result)
    model_data = await expensive_cache.get("ml_model_v1")
    print(f"   Model accuracy: {model_data['accuracy']}")
    
    print("\n3. Session Cache (30 minute TTL):")
    user_session = {
        "user_id": "user_123",
        "login_time": time.time(),
        "permissions": ["read", "write"],
        "preferences": {"theme": "dark"}
    }
    await session_cache.set("session_user_123", user_session)
    session = await session_cache.get("session_user_123")
    print(f"   User: {session['user_id']}, Permissions: {session['permissions']}")
    
    # Cleanup
    await expensive_cache.clear()


async def demo_cache_monitoring():
    """Demo cache monitoring and analytics"""
    print("\n📊 Cache Monitoring Demo")
    print("-" * 40)
    
    cache = MemoryCache(max_size=10, default_ttl=60)
    
    # Simulate usage patterns
    print("1. Simulating Usage Patterns:")
    
    # Add some data
    for i in range(5):
        await cache.set(f"item_{i}", f"data_{i}")
    
    # Simulate access patterns
    access_patterns = [
        ("item_0", 5),  # Hot data
        ("item_1", 3),  # Warm data
        ("item_2", 1),  # Cold data
        ("item_3", 2),  # Cool data
        ("item_4", 4),  # Hot data
    ]
    
    for item, access_count in access_patterns:
        for _ in range(access_count):
            await cache.get(item)
    
    print("   Simulated different access patterns")
    
    # Check cache statistics
    stats = cache.get_stats()
    print(f"\n2. Cache Statistics:")
    print(f"   Size: {stats['size']}/{stats['max_size']}")
    print(f"   Default TTL: {stats['default_ttl']}s")
    
    # Show access counts (internal inspection)
    print("\n3. Access Patterns:")
    for key, entry in cache.cache.items():
        print(f"   {key}: {entry.access_count} accesses")
    
    # Test eviction behavior
    print("\n4. Testing Eviction (adding more items):")
    for i in range(5, 15):  # Will trigger evictions
        await cache.set(f"new_item_{i}", f"new_data_{i}")
        if i % 2 == 0:  # Check intermediate state
            remaining_keys = [k for k in cache.cache.keys()]
            print(f"   After adding new_item_{i}: {len(remaining_keys)} items")


async def main():
    """Run all cache demos"""
    setup_logging()
    logger = get_logger(__name__)
    
    print("🚀 Advanced Cache System Demo")
    print("=" * 60)
    
    await demo_memory_cache()
    await demo_file_cache()
    await demo_response_cache()
    await demo_cache_strategies()
    await demo_cache_monitoring()
    
    print("\n🎉 Cache demo completed!")
    print("\nKey Benefits Demonstrated:")
    print("✅ Multi-level caching (Memory + File)")
    print("✅ TTL-based expiration")
    print("✅ LRU eviction strategy")
    print("✅ Persistent storage")
    print("✅ Provider-specific caching")
    print("✅ Automatic key generation")
    print("✅ Performance monitoring")
    print("✅ Thread-safe operations")


if __name__ == "__main__":
    asyncio.run(main())
