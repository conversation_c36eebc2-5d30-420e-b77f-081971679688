# Model Configuration
# Configure API keys and model parameters for different LLM providers

# OpenAI Configuration
openai:
  api_key: "${OPENAI_API_KEY}"  # Set via environment variable
  models:
    gpt-4:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
      frequency_penalty: 0.0
      presence_penalty: 0.0
    gpt-3.5-turbo:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
      frequency_penalty: 0.0
      presence_penalty: 0.0
  rate_limit:
    requests_per_minute: 60
    tokens_per_minute: 90000

# Anthropic Claude Configuration
anthropic:
  api_key: "${ANTHROPIC_API_KEY}"  # Set via environment variable
  models:
    claude-3-opus:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
    claude-3-sonnet:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
    claude-3-haiku:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
  rate_limit:
    requests_per_minute: 50
    tokens_per_minute: 40000

# Google Gemini Configuration
google:
  api_key: "${GOOGLE_API_KEY}"  # Set via environment variable
  models:
    gemini-pro:
      max_tokens: 8192
      temperature: 0.7
      top_p: 1.0
      top_k: 40
    gemini-pro-vision:
      max_tokens: 4096
      temperature: 0.7
      top_p: 1.0
      top_k: 40
    gemini-1.5-pro:
      max_tokens: 8192
      temperature: 0.7
      top_p: 1.0
      top_k: 40
  rate_limit:
    requests_per_minute: 60
    tokens_per_minute: 32000

# Default settings
defaults:
  provider: "openai"  # Default provider: openai, anthropic, google
  model: "gpt-3.5-turbo"  # Default model
  max_retries: 3
  timeout: 30  # seconds
  cache_enabled: true
  cache_ttl: 3600  # seconds (1 hour)

# Safety and content filtering
safety:
  content_filter: true
  max_input_length: 32000
  max_output_length: 8192
  blocked_categories:
    - "harassment"
    - "hate_speech"
    - "dangerous_content"