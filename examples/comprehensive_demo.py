#!/usr/bin/env python3
"""
Comprehensive Demo

Demonstrates all major features of the Generative AI framework.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from llm.openai_client import OpenA<PERSON>lient
from llm.claude_client import ClaudeClient
from llm.gemini_client import GeminiClient
from llm.base import CompletionRequest, ChatRequest, Message, MessageRole
from prompt_engineering.templates import TemplateManager
from prompt_engineering.few_shot import FewShotPromptBuilder, Example
from prompt_engineering.chain import ChainBuilder
from utils.logger import setup_logging, get_logger
from utils.cache import ResponseCache, MemoryCache
from utils.rate_limiter import APIRateLimiter, RateLimit
from utils.token_counter import TokenCounter, Provider
from handlers.error_handler import ErrorContext, handle_llm_errors


async def main():
    """Main demo function showcasing all features"""
    # Setup logging
    setup_logging()
    logger = get_logger(__name__)
    
    logger.info("Starting comprehensive demo of Generative AI framework")
    
    # Check for API keys
    providers = {}
    
    if os.getenv("OPENAI_API_KEY"):
        providers["OpenAI"] = OpenAIClient(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-3.5-turbo"
        )
    
    if os.getenv("ANTHROPIC_API_KEY"):
        providers["Claude"] = ClaudeClient(
            api_key=os.getenv("ANTHROPIC_API_KEY"),
            model="claude-3-haiku-20240307"
        )
    
    if os.getenv("GOOGLE_API_KEY"):
        providers["Gemini"] = GeminiClient(
            api_key=os.getenv("GOOGLE_API_KEY"),
            model="gemini-pro"
        )
    
    if not providers:
        logger.error("No API keys found. Please set at least one API key.")
        return
    
    print("🚀 Generative AI Framework - Comprehensive Demo")
    print("=" * 60)
    
    # Demo 1: Basic completions with multiple providers
    await demo_basic_completions(providers)
    
    # Demo 2: Template management
    await demo_template_management()
    
    # Demo 3: Few-shot learning
    await demo_few_shot_learning(list(providers.values())[0])
    
    # Demo 4: Prompt chaining
    await demo_prompt_chaining(list(providers.values())[0])
    
    # Demo 5: Caching
    await demo_caching(list(providers.values())[0])
    
    # Demo 6: Token counting and cost estimation
    await demo_token_counting()
    
    # Demo 7: Error handling
    await demo_error_handling(list(providers.values())[0])
    
    print("\n🎉 Demo completed successfully!")


async def demo_basic_completions(providers):
    """Demo basic completions with multiple providers"""
    print("\n📝 Demo 1: Basic Completions")
    print("-" * 40)
    
    prompt = "Write a haiku about programming."
    
    for name, client in providers.items():
        print(f"\n{name}:")
        
        request = CompletionRequest(
            prompt=prompt,
            max_tokens=100,
            temperature=0.7
        )
        
        async with ErrorContext({"provider": name}) as ctx:
            response = await client.complete(request)
            
            if ctx.error_info:
                print(f"Error: {ctx.error_info.message}")
            else:
                print(response.text)
                print(f"Tokens used: {response.usage['total_tokens']}")


async def demo_template_management():
    """Demo template management system"""
    print("\n📋 Demo 2: Template Management")
    print("-" * 40)
    
    # Load templates
    template_manager = TemplateManager()
    
    # List available templates
    print("Available templates:")
    for category in template_manager.list_categories():
        templates = template_manager.list_templates(category)
        print(f"  {category}: {len(templates)} templates")
    
    # Use a template
    try:
        rendered = template_manager.render_template(
            "completion.structured",
            task="Explain quantum computing",
            context="For a general audience",
            prompt="What is quantum computing and why is it important?"
        )
        print(f"\nRendered template:\n{rendered}")
    except Exception as e:
        print(f"Template error: {e}")


async def demo_few_shot_learning(client):
    """Demo few-shot learning"""
    print("\n🎯 Demo 3: Few-Shot Learning")
    print("-" * 40)
    
    # Create few-shot builder
    builder = FewShotPromptBuilder()
    
    # Add examples for sentiment analysis
    examples = [
        ("I love this product!", "Positive"),
        ("This is terrible.", "Negative"),
        ("It's okay, nothing special.", "Neutral"),
        ("Amazing quality and fast delivery!", "Positive"),
        ("Worst purchase ever.", "Negative")
    ]
    
    builder.add_examples(examples)
    
    # Build prompt for new query
    query = "The service was decent but could be better."
    prompt = builder.build_prompt(
        query,
        task_description="Classify the sentiment of the following text as Positive, Negative, or Neutral."
    )
    
    print("Few-shot prompt:")
    print(prompt)
    
    # Get response
    request = CompletionRequest(prompt=prompt, max_tokens=50, temperature=0.1)
    
    async with ErrorContext({"demo": "few_shot"}) as ctx:
        response = await client.complete(request)
        
        if not ctx.error_info:
            print(f"\nSentiment classification: {response.text.strip()}")


async def demo_prompt_chaining(client):
    """Demo prompt chaining"""
    print("\n🔗 Demo 4: Prompt Chaining")
    print("-" * 40)
    
    from examples.chain_prompts import LLMChainExecutor
    
    executor = LLMChainExecutor(client)
    
    # Create a simple analysis chain
    chain = (ChainBuilder("text_analysis", "Analyze and improve text")
             .step(
                 name="analyze",
                 template="Analyze this text for clarity and tone: {text}\n\nAnalysis:",
                 output_key="analysis",
                 required_inputs=["text"]
             )
             .step(
                 name="improve",
                 template="Based on this analysis: {analysis}\n\nImprove the original text: {text}\n\nImproved text:",
                 output_key="improved_text",
                 required_inputs=["analysis", "text"]
             )
             .build())
    
    # Execute chain
    initial_context = {
        "text": "AI is good. It helps people. Many use it now."
    }
    
    result = await chain.execute(executor, initial_context)
    
    print(f"Original: {initial_context['text']}")
    print(f"Analysis: {result.get('analysis', 'N/A')}")
    print(f"Improved: {result.get('improved_text', 'N/A')}")


async def demo_caching(client):
    """Demo caching functionality"""
    print("\n💾 Demo 5: Caching")
    print("-" * 40)
    
    cache = ResponseCache()
    
    prompt = "What is machine learning?"
    request_data = {
        "prompt": prompt,
        "max_tokens": 100,
        "temperature": 0.5
    }
    
    # First request (cache miss)
    print("First request (cache miss)...")
    start_time = asyncio.get_event_loop().time()
    
    cached_response = await cache.get_response("demo", "gpt-3.5-turbo", request_data)
    if cached_response is None:
        request = CompletionRequest(prompt=prompt, max_tokens=100, temperature=0.5)
        response = await client.complete(request)
        await cache.set_response("demo", "gpt-3.5-turbo", request_data, response.text)
        result = response.text
    else:
        result = cached_response
    
    first_time = asyncio.get_event_loop().time() - start_time
    print(f"Response: {result[:100]}...")
    print(f"Time: {first_time:.2f}s")
    
    # Second request (cache hit)
    print("\nSecond request (cache hit)...")
    start_time = asyncio.get_event_loop().time()
    
    cached_response = await cache.get_response("demo", "gpt-3.5-turbo", request_data)
    second_time = asyncio.get_event_loop().time() - start_time
    
    print(f"Response: {cached_response[:100]}...")
    print(f"Time: {second_time:.2f}s")
    print(f"Speedup: {first_time/second_time:.1f}x faster")


async def demo_token_counting():
    """Demo token counting and cost estimation"""
    print("\n🔢 Demo 6: Token Counting & Cost Estimation")
    print("-" * 40)
    
    counter = TokenCounter()
    
    text = "Artificial intelligence is transforming how we work, learn, and interact with technology."
    
    # Count tokens for different providers
    providers = [
        (Provider.OPENAI, "gpt-3.5-turbo"),
        (Provider.ANTHROPIC, "claude-3-haiku"),
        (Provider.GOOGLE, "gemini-pro")
    ]
    
    for provider, model in providers:
        tokens = counter.count_tokens(text, provider, model)
        
        # Estimate cost (assuming this is input tokens)
        from utils.token_counter import TokenUsage
        usage = TokenUsage(prompt_tokens=tokens, completion_tokens=50, total_tokens=tokens+50)
        cost = counter.estimate_cost(usage, provider, model)
        
        print(f"{provider.value} ({model}): {tokens} tokens, ~${cost:.4f}")


async def demo_error_handling(client):
    """Demo error handling capabilities"""
    print("\n⚠️  Demo 7: Error Handling")
    print("-" * 40)
    
    @handle_llm_errors(max_retries=2)
    async def test_function():
        # This will likely succeed, but demonstrates the retry mechanism
        request = CompletionRequest(
            prompt="Say hello",
            max_tokens=10,
            temperature=0
        )
        return await client.complete(request)
    
    try:
        response = await test_function()
        print(f"Success with retry decorator: {response.text.strip()}")
    except Exception as e:
        print(f"Failed after retries: {e}")
    
    # Test error context
    async with ErrorContext({"demo": "error_handling"}, suppress_errors=True) as ctx:
        # Simulate an error by using invalid parameters
        request = CompletionRequest(
            prompt="",  # Empty prompt might cause validation error
            max_tokens=-1,  # Invalid max_tokens
            temperature=2.0  # Invalid temperature
        )
        
        try:
            await client.complete(request)
        except:
            pass  # Error will be handled by context
    
    if ctx.error_info:
        print(f"Caught error: {ctx.error_info.error_type.value}")
        print(f"Message: {ctx.error_info.message}")


if __name__ == "__main__":
    asyncio.run(main())
