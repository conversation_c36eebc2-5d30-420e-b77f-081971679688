"""
Google Gemini Client

Client implementation for Google's Gemini API.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
import time

from .base import (
    BaseLLMClient, 
    CompletionRequest, 
    ChatRequest, 
    CompletionResponse, 
    ChatResponse, 
    Message, 
    MessageRole
)

logger = logging.getLogger(__name__)


class GeminiClient(BaseLLMClient):
    """
    Google Gemini API client implementation.
    """
    
    def __init__(self, api_key: str, model: str = "gemini-pro", **kwargs):
        """
        Initialize Gemini client.
        
        Args:
            api_key: Google API key
            model: Gemini model name (gemini-pro, gemini-pro-vision, etc.)
            **kwargs: Additional configuration
        """
        super().__init__(api_key, model, **kwargs)
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Initialize the model
        self.client = genai.GenerativeModel(model)
        
        # Gemini-specific parameters
        self.top_k = kwargs.get('top_k', 40)
        
        # Safety settings
        self.safety_settings = kwargs.get('safety_settings', {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        })
        
        self.logger.info(f"Initialized Gemini client with model: {model}")
    
    async def complete(self, request: CompletionRequest) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """
        Generate text completion using Gemini.
        
        Args:
            request: Completion request
            
        Returns:
            CompletionResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)
        
        start_time = time.time()
        
        try:
            # Prepare generation config
            generation_config = self._prepare_generation_config(request)
            
            if request.stream:
                return self._stream_completion(request, generation_config)
            else:
                response = await self._generate_completion(request, generation_config)
                response_time = time.time() - start_time
                
                completion_response = CompletionResponse(
                    text=response.text,
                    model=self.model,
                    usage=self._extract_usage(response),
                    finish_reason=self._get_finish_reason(response),
                    response_time=response_time,
                    metadata={"candidates": len(response.candidates)}
                )
                
                self._log_response(completion_response)
                return completion_response
                
        except Exception as e:
            self.logger.error(f"Completion request failed: {e}")
            raise
    
    async def chat(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator[Message, None]]:
        """
        Generate chat completion using Gemini.
        
        Args:
            request: Chat request
            
        Returns:
            ChatResponse or AsyncGenerator for streaming
        """
        self._log_request(request)
        self.validate_request(request)
        
        start_time = time.time()
        
        try:
            # Convert messages to Gemini format
            history = self._convert_messages_to_history(request.messages)
            
            # Start chat session
            chat = self.client.start_chat(history=history[:-1])  # Exclude last message
            
            # Prepare generation config
            generation_config = self._prepare_generation_config(request)
            
            if request.stream:
                return self._stream_chat(chat, request, generation_config)
            else:
                # Send the last message
                last_message = history[-1]['parts'][0]
                response = await self._generate_chat_response(chat, last_message, generation_config)
                response_time = time.time() - start_time
                
                chat_response = ChatResponse(
                    message=Message(
                        role=MessageRole.ASSISTANT,
                        content=response.text
                    ),
                    model=self.model,
                    usage=self._extract_usage(response),
                    finish_reason=self._get_finish_reason(response),
                    response_time=response_time,
                    metadata={"candidates": len(response.candidates)}
                )
                
                self._log_response(chat_response)
                return chat_response
                
        except Exception as e:
            self.logger.error(f"Chat request failed: {e}")
            raise
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text using Gemini's tokenizer.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        try:
            result = self.client.count_tokens(text)
            return result.total_tokens
        except Exception as e:
            self.logger.warning(f"Token counting failed, using approximation: {e}")
            # Fallback: rough approximation (1 token ≈ 4 characters for English)
            return len(text) // 4
    
    def validate_request(self, request: Union[CompletionRequest, ChatRequest]) -> bool:
        """
        Validate request parameters for Gemini.
        
        Args:
            request: Request to validate
            
        Returns:
            True if valid
            
        Raises:
            ValueError: If request is invalid
        """
        if isinstance(request, CompletionRequest):
            if not request.prompt or not request.prompt.strip():
                raise ValueError("Prompt cannot be empty")
            
            # Check token limit
            token_count = self.count_tokens(request.prompt)
            if token_count > 30000:  # Gemini's context limit
                raise ValueError(f"Prompt too long: {token_count} tokens (max: 30000)")
                
        elif isinstance(request, ChatRequest):
            if not request.messages:
                raise ValueError("Messages cannot be empty")
            
            # Check total token count
            total_text = " ".join([msg.content for msg in request.messages])
            token_count = self.count_tokens(total_text)
            if token_count > 30000:
                raise ValueError(f"Conversation too long: {token_count} tokens (max: 30000)")
        
        return True
    
    def _prepare_generation_config(self, request: Union[CompletionRequest, ChatRequest]) -> Dict[str, Any]:
        """Prepare generation configuration for Gemini"""
        params = self._merge_params({
            'max_tokens': request.max_tokens,
            'temperature': request.temperature,
            'top_p': request.top_p,
        })
        
        config = {
            'max_output_tokens': params['max_tokens'],
            'temperature': params['temperature'],
            'top_p': params['top_p'],
            'top_k': self.top_k,
        }
        
        if hasattr(request, 'stop') and request.stop:
            config['stop_sequences'] = request.stop
        
        return config
    
    async def _generate_completion(self, request: CompletionRequest, generation_config: Dict[str, Any]):
        """Generate completion response"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.client.generate_content(
                request.prompt,
                generation_config=generation_config,
                safety_settings=self.safety_settings
            )
        )
    
    async def _generate_chat_response(self, chat, message: str, generation_config: Dict[str, Any]):
        """Generate chat response"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: chat.send_message(
                message,
                generation_config=generation_config,
                safety_settings=self.safety_settings
            )
        )
    
    def _convert_messages_to_history(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert messages to Gemini chat history format"""
        history = []
        
        for message in messages:
            if message.role == MessageRole.SYSTEM:
                # Gemini doesn't have system role, prepend to first user message
                continue
            
            role = "user" if message.role == MessageRole.USER else "model"
            history.append({
                "role": role,
                "parts": [message.content]
            })
        
        return history
    
    def _extract_usage(self, response) -> Dict[str, int]:
        """Extract usage information from response"""
        try:
            if hasattr(response, 'usage_metadata'):
                return {
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "completion_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.total_token_count
                }
        except:
            pass
        
        # Fallback
        return {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
    
    def _get_finish_reason(self, response) -> str:
        """Extract finish reason from response"""
        try:
            if response.candidates:
                return response.candidates[0].finish_reason.name.lower()
        except:
            pass
        
        return "unknown"
    
    async def _stream_completion(self, request: CompletionRequest, generation_config: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream completion response"""
        try:
            response = self.client.generate_content(
                request.prompt,
                generation_config=generation_config,
                safety_settings=self.safety_settings,
                stream=True
            )
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
        except Exception as e:
            self.logger.error(f"Streaming completion failed: {e}")
            raise
    
    async def _stream_chat(self, chat, request: ChatRequest, generation_config: Dict[str, Any]) -> AsyncGenerator[Message, None]:
        """Stream chat response"""
        try:
            last_message = request.messages[-1].content
            response = chat.send_message(
                last_message,
                generation_config=generation_config,
                safety_settings=self.safety_settings,
                stream=True
            )
            
            for chunk in response:
                if chunk.text:
                    yield Message(
                        role=MessageRole.ASSISTANT,
                        content=chunk.text
                    )
        except Exception as e:
            self.logger.error(f"Streaming chat failed: {e}")
            raise
