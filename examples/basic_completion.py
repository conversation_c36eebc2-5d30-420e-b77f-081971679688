#!/usr/bin/env python3
"""
Basic Completion Example

Demonstrates basic text completion using different LLM providers.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from llm.openai_client import OpenAIClient
from llm.claude_client import Claude<PERSON><PERSON>
from llm.gemini_client import Gemini<PERSON>lient
from llm.base import CompletionRequest
from utils.logger import setup_logging, get_logger
from handlers.error_handler import handle_llm_errors, ErrorContext


async def main():
    """Main example function"""
    # Setup logging
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting basic completion example")

    # Example prompt
    prompt = "Write a short poem about artificial intelligence and creativity."

    # Test different providers
    providers = []

    # OpenAI
    if os.getenv("OPENAI_API_KEY"):
        providers.append(("OpenAI", OpenAIClient(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-3.5-turbo"
        )))

    # Claude
    if os.getenv("ANTHROPIC_API_KEY"):
        providers.append(("Claude", ClaudeClient(
            api_key=os.getenv("ANTHROPIC_API_KEY"),
            model="claude-3-haiku-20240307"
        )))

    # Gemini
    if os.getenv("GOOGLE_API_KEY"):
        providers.append(("Gemini", GeminiClient(
            api_key=os.getenv("GOOGLE_API_KEY"),
            model="gemini-pro"
        )))

    if not providers:
        logger.error("No API keys found. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_API_KEY")
        return

    # Test each provider
    for provider_name, client in providers:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {provider_name}")
        logger.info(f"{'='*50}")

        try:
            # Create completion request
            request = CompletionRequest(
                prompt=prompt,
                max_tokens=200,
                temperature=0.7
            )

            # Use error context for safe execution
            async with ErrorContext({"provider": provider_name}) as ctx:
                # Test basic completion
                logger.info("Testing basic completion...")
                response = await client.complete(request)

                logger.info(f"Response from {provider_name}:")
                logger.info(f"Text: {response.text}")
                logger.info(f"Usage: {response.usage}")
                logger.info(f"Response time: {response.response_time:.2f}s")

                # Test streaming completion
                logger.info("\nTesting streaming completion...")
                request.stream = True

                print(f"\nStreaming response from {provider_name}:")
                async for chunk in await client.complete(request):
                    print(chunk, end="", flush=True)
                print("\n")

            if ctx.error_info:
                logger.error(f"Error with {provider_name}: {ctx.error_info.message}")

        except Exception as e:
            logger.error(f"Failed to test {provider_name}: {e}")

    logger.info("Basic completion example completed")


@handle_llm_errors(max_retries=2)
async def test_with_retry(client, request):
    """Test function with automatic retry on errors"""
    return await client.complete(request)


async def advanced_example():
    """Advanced example with error handling and retries"""
    setup_logging()
    logger = get_logger(__name__)

    logger.info("Starting advanced completion example with retry logic")

    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY not found")
        return

    client = OpenAIClient(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="gpt-3.5-turbo"
    )

    # Test with retry decorator
    request = CompletionRequest(
        prompt="Explain quantum computing in simple terms.",
        max_tokens=150,
        temperature=0.5
    )

    try:
        response = await test_with_retry(client, request)
        logger.info(f"Response: {response.text}")
    except Exception as e:
        logger.error(f"All retries failed: {e}")


if __name__ == "__main__":
    # Run basic example
    asyncio.run(main())

    # Run advanced example
    print("\n" + "="*60)
    asyncio.run(advanced_example())