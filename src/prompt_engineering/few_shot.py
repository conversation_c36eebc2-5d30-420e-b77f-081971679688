"""
Few-Shot Learning Utilities

Utilities for creating and managing few-shot prompts.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod
import random


logger = logging.getLogger(__name__)


@dataclass
class Example:
    """Single example for few-shot learning"""
    input: str
    output: str
    metadata: Optional[Dict[str, Any]] = None

    def format(self, input_label: str = "Input", output_label: str = "Output") -> str:
        """
        Format example as text.

        Args:
            input_label: Label for input
            output_label: Label for output

        Returns:
            Formatted example
        """
        return f"{input_label}: {self.input}\n{output_label}: {self.output}"


@dataclass
class FewShotConfig:
    """Configuration for few-shot prompt generation"""
    max_examples: int = 5
    input_label: str = "Input"
    output_label: str = "Output"
    example_separator: str = "\n\n"
    shuffle_examples: bool = False
    include_instructions: bool = True
    instruction_template: str = "Here are some examples:"


class ExampleSelector(ABC):
    """Abstract base class for example selection strategies"""

    @abstractmethod
    def select_examples(self, examples: List[Example], query: str, max_examples: int) -> List[Example]:
        """
        Select examples for few-shot prompt.

        Args:
            examples: Available examples
            query: Current query/input
            max_examples: Maximum number of examples to select

        Returns:
            Selected examples
        """
        pass


class RandomSelector(ExampleSelector):
    """Random example selection"""

    def __init__(self, seed: Optional[int] = None):
        """
        Initialize random selector.

        Args:
            seed: Random seed for reproducibility
        """
        self.seed = seed
        if seed is not None:
            random.seed(seed)

    def select_examples(self, examples: List[Example], query: str, max_examples: int) -> List[Example]:
        """Select random examples"""
        if len(examples) <= max_examples:
            return examples.copy()

        return random.sample(examples, max_examples)


class SimilaritySelector(ExampleSelector):
    """Similarity-based example selection"""

    def __init__(self, similarity_fn: Optional[callable] = None):
        """
        Initialize similarity selector.

        Args:
            similarity_fn: Function to compute similarity between strings
        """
        self.similarity_fn = similarity_fn or self._default_similarity

    def select_examples(self, examples: List[Example], query: str, max_examples: int) -> List[Example]:
        """Select examples based on similarity to query"""
        if len(examples) <= max_examples:
            return examples.copy()

        # Calculate similarities
        similarities = []
        for example in examples:
            similarity = self.similarity_fn(query, example.input)
            similarities.append((similarity, example))

        # Sort by similarity (descending) and take top examples
        similarities.sort(key=lambda x: x[0], reverse=True)
        return [example for _, example in similarities[:max_examples]]

    def _default_similarity(self, text1: str, text2: str) -> float:
        """Default similarity function using simple word overlap"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)  # Jaccard similarity


class LengthSelector(ExampleSelector):
    """Select examples based on length similarity"""

    def select_examples(self, examples: List[Example], query: str, max_examples: int) -> List[Example]:
        """Select examples with similar length to query"""
        if len(examples) <= max_examples:
            return examples.copy()

        query_length = len(query)

        # Calculate length differences
        length_diffs = []
        for example in examples:
            diff = abs(len(example.input) - query_length)
            length_diffs.append((diff, example))

        # Sort by length difference (ascending) and take closest examples
        length_diffs.sort(key=lambda x: x[0])
        return [example for _, example in length_diffs[:max_examples]]


class FewShotPromptBuilder:
    """
    Builder for creating few-shot prompts.
    """

    def __init__(self, config: Optional[FewShotConfig] = None,
                 selector: Optional[ExampleSelector] = None):
        """
        Initialize few-shot prompt builder.

        Args:
            config: Configuration for prompt building
            selector: Example selection strategy
        """
        self.config = config or FewShotConfig()
        self.selector = selector or RandomSelector()
        self.examples: List[Example] = []
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def add_example(self, input_text: str, output_text: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add an example to the example pool.

        Args:
            input_text: Example input
            output_text: Example output
            metadata: Optional metadata
        """
        example = Example(input=input_text, output=output_text, metadata=metadata)
        self.examples.append(example)
        self.logger.debug(f"Added example: {len(self.examples)} total examples")

    def add_examples(self, examples: List[Tuple[str, str]]) -> None:
        """
        Add multiple examples at once.

        Args:
            examples: List of (input, output) tuples
        """
        for input_text, output_text in examples:
            self.add_example(input_text, output_text)

    def load_examples_from_dict(self, examples_dict: List[Dict[str, str]]) -> None:
        """
        Load examples from dictionary format.

        Args:
            examples_dict: List of dictionaries with 'input' and 'output' keys
        """
        for example_data in examples_dict:
            input_text = example_data.get('input', '')
            output_text = example_data.get('output', '')
            metadata = {k: v for k, v in example_data.items() if k not in ['input', 'output']}

            self.add_example(input_text, output_text, metadata if metadata else None)

    def build_prompt(self, query: str, task_description: Optional[str] = None) -> str:
        """
        Build few-shot prompt for given query.

        Args:
            query: Current query/input
            task_description: Optional task description

        Returns:
            Complete few-shot prompt
        """
        if not self.examples:
            self.logger.warning("No examples available for few-shot prompt")
            return query

        # Select examples
        selected_examples = self.selector.select_examples(
            self.examples, query, self.config.max_examples
        )

        if self.config.shuffle_examples:
            random.shuffle(selected_examples)

        # Build prompt parts
        prompt_parts = []

        # Add task description if provided
        if task_description:
            prompt_parts.append(task_description)

        # Add instruction if enabled
        if self.config.include_instructions and selected_examples:
            prompt_parts.append(self.config.instruction_template)

        # Add examples
        if selected_examples:
            example_texts = []
            for example in selected_examples:
                example_text = example.format(
                    self.config.input_label,
                    self.config.output_label
                )
                example_texts.append(example_text)

            examples_section = self.config.example_separator.join(example_texts)
            prompt_parts.append(examples_section)

        # Add current query
        query_text = f"{self.config.input_label}: {query}\n{self.config.output_label}:"
        prompt_parts.append(query_text)

        # Join all parts
        full_prompt = "\n\n".join(prompt_parts)

        self.logger.debug(f"Built few-shot prompt with {len(selected_examples)} examples")
        return full_prompt

    def get_example_count(self) -> int:
        """Get total number of examples"""
        return len(self.examples)

    def clear_examples(self) -> None:
        """Clear all examples"""
        self.examples.clear()
        self.logger.info("Cleared all examples")

    def get_examples_by_metadata(self, key: str, value: Any) -> List[Example]:
        """
        Get examples filtered by metadata.

        Args:
            key: Metadata key
            value: Metadata value

        Returns:
            Filtered examples
        """
        filtered = []
        for example in self.examples:
            if example.metadata and example.metadata.get(key) == value:
                filtered.append(example)

        return filtered