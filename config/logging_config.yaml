# Logging Configuration
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

  json:
    format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "message": "%(message)s"}'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  api_file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: json
    filename: logs/api.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

loggers:
  # Root logger
  '':
    level: INFO
    handlers: [console, file]
    propagate: false

  # LLM clients
  llm:
    level: DEBUG
    handlers: [console, file, api_file]
    propagate: false

  llm.openai:
    level: DEBUG
    handlers: [api_file]
    propagate: true

  llm.anthropic:
    level: DEBUG
    handlers: [api_file]
    propagate: true

  llm.google:
    level: DEBUG
    handlers: [api_file]
    propagate: true

  # Prompt engineering
  prompt_engineering:
    level: INFO
    handlers: [console, file]
    propagate: false

  # Utils
  utils:
    level: INFO
    handlers: [console, file]
    propagate: false

  utils.rate_limiter:
    level: DEBUG
    handlers: [file]
    propagate: true

  utils.cache:
    level: INFO
    handlers: [file]
    propagate: true

  # Error handling
  error_handler:
    level: ERROR
    handlers: [console, error_file]
    propagate: false

# Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL