# Environment Variables for Generative AI Project
# Copy this file to .env and fill in your API keys

# OpenAI API Key
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude API Key  
# Get your key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini API Key
# Get your key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Optional: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Optional: Cache settings
CACHE_ENABLED=true
CACHE_TTL=3600

# Optional: Rate limiting settings
RATE_LIMIT_ENABLED=true
