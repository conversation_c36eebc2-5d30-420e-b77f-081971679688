"""
Prompt Chaining

Utilities for chaining multiple prompts together in sequence.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum


logger = logging.getLogger(__name__)


class ChainStepStatus(Enum):
    """Status of a chain step"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ChainStepResult:
    """Result of a chain step execution"""
    step_name: str
    status: ChainStepStatus
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ChainStep:
    """Single step in a prompt chain"""
    name: str
    template: str
    description: str = ""
    required_inputs: List[str] = field(default_factory=list)
    output_key: str = "result"
    condition: Optional[Callable[[Dict[str, Any]], bool]] = None
    retry_count: int = 0
    timeout: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

    def should_execute(self, context: Dict[str, Any]) -> bool:
        """
        Check if step should be executed based on condition.

        Args:
            context: Current execution context

        Returns:
            True if step should be executed
        """
        if self.condition is None:
            return True

        try:
            return self.condition(context)
        except Exception as e:
            logger.warning(f"Error evaluating condition for step {self.name}: {e}")
            return True  # Default to executing if condition fails

    def validate_inputs(self, context: Dict[str, Any]) -> List[str]:
        """
        Validate that required inputs are available in context.

        Args:
            context: Current execution context

        Returns:
            List of missing input keys
        """
        missing = []
        for required_input in self.required_inputs:
            if required_input not in context:
                missing.append(required_input)

        return missing

    def render_template(self, context: Dict[str, Any]) -> str:
        """
        Render template with context variables.

        Args:
            context: Context variables

        Returns:
            Rendered template
        """
        try:
            return self.template.format(**context)
        except KeyError as e:
            raise ValueError(f"Missing template variable in step {self.name}: {e}")


class ChainExecutor(ABC):
    """Abstract base class for chain executors"""

    @abstractmethod
    async def execute_step(self, step: ChainStep, prompt: str, context: Dict[str, Any]) -> str:
        """
        Execute a single chain step.

        Args:
            step: Chain step to execute
            prompt: Rendered prompt
            context: Execution context

        Returns:
            Step result
        """
        pass


class PromptChain:
    """
    Prompt chain for sequential execution of multiple prompts.
    """

    def __init__(self, name: str, description: str = ""):
        """
        Initialize prompt chain.

        Args:
            name: Chain name
            description: Chain description
        """
        self.name = name
        self.description = description
        self.steps: List[ChainStep] = []
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def add_step(self, step: ChainStep) -> None:
        """
        Add a step to the chain.

        Args:
            step: Chain step to add
        """
        self.steps.append(step)
        self.logger.debug(f"Added step '{step.name}' to chain '{self.name}'")

    def add_simple_step(self, name: str, template: str, output_key: str = "result",
                       required_inputs: Optional[List[str]] = None, **kwargs) -> None:
        """
        Add a simple step to the chain.

        Args:
            name: Step name
            template: Prompt template
            output_key: Key to store result in context
            required_inputs: Required input keys
            **kwargs: Additional step parameters
        """
        step = ChainStep(
            name=name,
            template=template,
            output_key=output_key,
            required_inputs=required_inputs or [],
            **kwargs
        )
        self.add_step(step)

    async def execute(self, executor: ChainExecutor, initial_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute the entire chain.

        Args:
            executor: Chain executor
            initial_context: Initial context variables

        Returns:
            Final execution context with all results
        """
        context = initial_context.copy() if initial_context else {}
        results = []

        self.logger.info(f"Starting execution of chain '{self.name}' with {len(self.steps)} steps")

        for i, step in enumerate(self.steps):
            self.logger.debug(f"Executing step {i+1}/{len(self.steps)}: {step.name}")

            result = await self._execute_step(executor, step, context)
            results.append(result)

            # Update context with step result
            if result.status == ChainStepStatus.COMPLETED and result.output_data:
                context.update(result.output_data)
            elif result.status == ChainStepStatus.FAILED:
                self.logger.error(f"Chain execution failed at step '{step.name}': {result.error}")
                break

        # Add execution metadata to context
        context['_chain_results'] = results
        context['_chain_name'] = self.name

        self.logger.info(f"Chain '{self.name}' execution completed")
        return context

    async def _execute_step(self, executor: ChainExecutor, step: ChainStep, context: Dict[str, Any]) -> ChainStepResult:
        """Execute a single step"""
        import time

        start_time = time.time()

        # Check if step should be executed
        if not step.should_execute(context):
            self.logger.debug(f"Skipping step '{step.name}' due to condition")
            return ChainStepResult(
                step_name=step.name,
                status=ChainStepStatus.SKIPPED,
                input_data=context.copy(),
                execution_time=time.time() - start_time
            )

        # Validate inputs
        missing_inputs = step.validate_inputs(context)
        if missing_inputs:
            error_msg = f"Missing required inputs: {missing_inputs}"
            self.logger.error(f"Step '{step.name}' failed: {error_msg}")
            return ChainStepResult(
                step_name=step.name,
                status=ChainStepStatus.FAILED,
                input_data=context.copy(),
                error=error_msg,
                execution_time=time.time() - start_time
            )

        # Execute step with retries
        for attempt in range(step.retry_count + 1):
            try:
                # Render template
                prompt = step.render_template(context)

                # Execute step
                if step.timeout:
                    result = await asyncio.wait_for(
                        executor.execute_step(step, prompt, context),
                        timeout=step.timeout
                    )
                else:
                    result = await executor.execute_step(step, prompt, context)

                # Success
                output_data = {step.output_key: result}

                return ChainStepResult(
                    step_name=step.name,
                    status=ChainStepStatus.COMPLETED,
                    input_data=context.copy(),
                    output_data=output_data,
                    execution_time=time.time() - start_time
                )

            except Exception as e:
                error_msg = f"Attempt {attempt + 1} failed: {str(e)}"
                self.logger.warning(f"Step '{step.name}' {error_msg}")

                if attempt == step.retry_count:
                    # Final failure
                    return ChainStepResult(
                        step_name=step.name,
                        status=ChainStepStatus.FAILED,
                        input_data=context.copy(),
                        error=error_msg,
                        execution_time=time.time() - start_time
                    )

                # Wait before retry
                await asyncio.sleep(1.0 * (attempt + 1))

    def get_step_count(self) -> int:
        """Get number of steps in chain"""
        return len(self.steps)

    def get_step(self, name: str) -> Optional[ChainStep]:
        """Get step by name"""
        for step in self.steps:
            if step.name == name:
                return step
        return None

    def remove_step(self, name: str) -> bool:
        """Remove step by name"""
        for i, step in enumerate(self.steps):
            if step.name == name:
                del self.steps[i]
                self.logger.debug(f"Removed step '{name}' from chain '{self.name}'")
                return True
        return False

    def clear_steps(self) -> None:
        """Clear all steps"""
        self.steps.clear()
        self.logger.debug(f"Cleared all steps from chain '{self.name}'")


class ChainBuilder:
    """
    Builder for creating prompt chains with fluent interface.
    """

    def __init__(self, name: str, description: str = ""):
        """
        Initialize chain builder.

        Args:
            name: Chain name
            description: Chain description
        """
        self.chain = PromptChain(name, description)

    def step(self, name: str, template: str, **kwargs) -> 'ChainBuilder':
        """
        Add a step to the chain.

        Args:
            name: Step name
            template: Prompt template
            **kwargs: Additional step parameters

        Returns:
            Self for chaining
        """
        self.chain.add_simple_step(name, template, **kwargs)
        return self

    def conditional_step(self, name: str, template: str, condition: Callable[[Dict[str, Any]], bool],
                        **kwargs) -> 'ChainBuilder':
        """
        Add a conditional step to the chain.

        Args:
            name: Step name
            template: Prompt template
            condition: Condition function
            **kwargs: Additional step parameters

        Returns:
            Self for chaining
        """
        kwargs['condition'] = condition
        self.chain.add_simple_step(name, template, **kwargs)
        return self

    def build(self) -> PromptChain:
        """
        Build and return the chain.

        Returns:
            Completed prompt chain
        """
        return self.chain