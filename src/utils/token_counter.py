"""
Token Counter Utilities

Token counting and cost estimation for different LLM providers.
"""

import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False


logger = logging.getLogger(__name__)


class Provider(Enum):
    """LLM Provider enumeration"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"


@dataclass
class TokenUsage:
    """Token usage information"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    estimated_cost: Optional[float] = None


@dataclass
class ModelPricing:
    """Model pricing information (per 1K tokens)"""
    input_cost: float  # Cost per 1K input tokens
    output_cost: float  # Cost per 1K output tokens


class TokenCounter:
    """
    Universal token counter for different LLM providers.
    """

    # Pricing information (per 1K tokens in USD)
    PRICING = {
        Provider.OPENAI: {
            "gpt-4": ModelPricing(0.03, 0.06),
            "gpt-4-32k": ModelPricing(0.06, 0.12),
            "gpt-4-turbo": ModelPricing(0.01, 0.03),
            "gpt-3.5-turbo": ModelPricing(0.0015, 0.002),
            "gpt-3.5-turbo-16k": ModelPricing(0.003, 0.004),
        },
        Provider.ANTHROPIC: {
            "claude-3-opus": ModelPricing(0.015, 0.075),
            "claude-3-sonnet": ModelPricing(0.003, 0.015),
            "claude-3-haiku": ModelPricing(0.00025, 0.00125),
            "claude-2.1": ModelPricing(0.008, 0.024),
            "claude-2.0": ModelPricing(0.008, 0.024),
            "claude-instant-1.2": ModelPricing(0.0008, 0.0024),
        },
        Provider.GOOGLE: {
            "gemini-pro": ModelPricing(0.0005, 0.0015),
            "gemini-pro-vision": ModelPricing(0.0005, 0.0015),
            "gemini-1.5-pro": ModelPricing(0.0035, 0.0105),
        }
    }

    def __init__(self):
        """Initialize token counter"""
        self.openai_encoders = {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        if TIKTOKEN_AVAILABLE:
            self.logger.info("tiktoken available for OpenAI token counting")
        else:
            self.logger.warning("tiktoken not available, using approximations for OpenAI")

    def count_tokens(self, text: str, provider: Provider, model: str) -> int:
        """
        Count tokens for given text and model.

        Args:
            text: Text to count tokens for
            provider: LLM provider
            model: Model name

        Returns:
            Number of tokens
        """
        if provider == Provider.OPENAI:
            return self._count_openai_tokens(text, model)
        elif provider == Provider.ANTHROPIC:
            return self._count_anthropic_tokens(text, model)
        elif provider == Provider.GOOGLE:
            return self._count_google_tokens(text, model)
        else:
            # Fallback approximation
            return self._approximate_tokens(text)

    def count_messages_tokens(self, messages: List[Dict[str, str]], provider: Provider, model: str) -> int:
        """
        Count tokens for a list of messages.

        Args:
            messages: List of messages with 'role' and 'content'
            provider: LLM provider
            model: Model name

        Returns:
            Number of tokens
        """
        if provider == Provider.OPENAI:
            return self._count_openai_messages_tokens(messages, model)
        else:
            # For other providers, sum individual message tokens
            total_tokens = 0
            for message in messages:
                total_tokens += self.count_tokens(message.get('content', ''), provider, model)
            return total_tokens

    def estimate_cost(self, usage: TokenUsage, provider: Provider, model: str) -> float:
        """
        Estimate cost for token usage.

        Args:
            usage: Token usage information
            provider: LLM provider
            model: Model name

        Returns:
            Estimated cost in USD
        """
        if provider not in self.PRICING or model not in self.PRICING[provider]:
            self.logger.warning(f"No pricing info for {provider.value}/{model}")
            return 0.0

        pricing = self.PRICING[provider][model]

        input_cost = (usage.prompt_tokens / 1000) * pricing.input_cost
        output_cost = (usage.completion_tokens / 1000) * pricing.output_cost

        return input_cost + output_cost

    def _count_openai_tokens(self, text: str, model: str) -> int:
        """Count tokens for OpenAI models"""
        if not TIKTOKEN_AVAILABLE:
            return self._approximate_tokens(text)

        try:
            if model not in self.openai_encoders:
                try:
                    self.openai_encoders[model] = tiktoken.encoding_for_model(model)
                except KeyError:
                    # Fallback to cl100k_base for unknown models
                    self.openai_encoders[model] = tiktoken.get_encoding("cl100k_base")

            encoder = self.openai_encoders[model]
            return len(encoder.encode(text))

        except Exception as e:
            self.logger.warning(f"Error counting OpenAI tokens: {e}")
            return self._approximate_tokens(text)

    def _count_openai_messages_tokens(self, messages: List[Dict[str, str]], model: str) -> int:
        """Count tokens for OpenAI chat messages with formatting overhead"""
        if not TIKTOKEN_AVAILABLE:
            total_text = " ".join([msg.get('content', '') for msg in messages])
            return self._approximate_tokens(total_text)

        try:
            if model not in self.openai_encoders:
                try:
                    self.openai_encoders[model] = tiktoken.encoding_for_model(model)
                except KeyError:
                    self.openai_encoders[model] = tiktoken.get_encoding("cl100k_base")

            encoder = self.openai_encoders[model]

            # Count tokens with chat formatting overhead
            num_tokens = 0

            for message in messages:
                num_tokens += 4  # Every message has overhead
                for key, value in message.items():
                    num_tokens += len(encoder.encode(str(value)))
                    if key == "name":  # Name field has additional overhead
                        num_tokens += 1

            num_tokens += 2  # Every reply is primed with assistant

            return num_tokens

        except Exception as e:
            self.logger.warning(f"Error counting OpenAI message tokens: {e}")
            total_text = " ".join([msg.get('content', '') for msg in messages])
            return self._approximate_tokens(total_text)

    def _count_anthropic_tokens(self, text: str, model: str) -> int:
        """Count tokens for Anthropic models (approximation)"""
        # Anthropic doesn't provide a public tokenizer
        # Use approximation: ~3.5 characters per token for English
        return int(len(text) / 3.5)

    def _count_google_tokens(self, text: str, model: str) -> int:
        """Count tokens for Google models"""
        if not GENAI_AVAILABLE:
            return self._approximate_tokens(text)

        try:
            # This would require a configured Gemini client
            # For now, use approximation
            return self._approximate_tokens(text)
        except Exception as e:
            self.logger.warning(f"Error counting Google tokens: {e}")
            return self._approximate_tokens(text)

    def _approximate_tokens(self, text: str) -> int:
        """Rough token approximation for any text"""
        # General approximation: ~4 characters per token for English
        return max(1, len(text) // 4)

    def get_model_limits(self, provider: Provider, model: str) -> Dict[str, int]:
        """
        Get model context limits.

        Args:
            provider: LLM provider
            model: Model name

        Returns:
            Dictionary with context limits
        """
        limits = {
            Provider.OPENAI: {
                "gpt-4": {"context": 8192, "output": 4096},
                "gpt-4-32k": {"context": 32768, "output": 4096},
                "gpt-4-turbo": {"context": 128000, "output": 4096},
                "gpt-3.5-turbo": {"context": 4096, "output": 4096},
                "gpt-3.5-turbo-16k": {"context": 16384, "output": 4096},
            },
            Provider.ANTHROPIC: {
                "claude-3-opus": {"context": 200000, "output": 4096},
                "claude-3-sonnet": {"context": 200000, "output": 4096},
                "claude-3-haiku": {"context": 200000, "output": 4096},
                "claude-2.1": {"context": 200000, "output": 4096},
                "claude-2.0": {"context": 100000, "output": 4096},
                "claude-instant-1.2": {"context": 100000, "output": 4096},
            },
            Provider.GOOGLE: {
                "gemini-pro": {"context": 30720, "output": 8192},
                "gemini-pro-vision": {"context": 30720, "output": 4096},
                "gemini-1.5-pro": {"context": 1000000, "output": 8192},
            }
        }

        return limits.get(provider, {}).get(model, {"context": 4096, "output": 1000})