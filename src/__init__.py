"""
Generative AI Project

A comprehensive framework for building generative AI applications with multiple LLM providers.
"""

__version__ = "1.0.0"
__author__ = "Generative AI Project Team"

# Import main components for easy access
from .llm.base import BaseLLMClient, CompletionRequest, ChatRequest, Message, MessageRole
from .llm.openai_client import OpenAIClient
from .llm.claude_client import Claude<PERSON><PERSON>
from .llm.gemini_client import Gemini<PERSON>lient

from .prompt_engineering.templates import TemplateManager, PromptTemplate
from .prompt_engineering.few_shot import FewShotPromptBuilder, Example
from .prompt_engineering.chain import Prompt<PERSON>hain, ChainBuilder, ChainExecutor

from .utils.logger import setup_logging, get_logger
from .utils.cache import MemoryCache, FileCache, ResponseCache
from .utils.rate_limiter import APIRateLimiter, RateLimit
from .utils.token_counter import TokenCounter, Provider

from .handlers.error_handler import (
    LLMError, APIError, RateLimitError, Authentication<PERSON>rro<PERSON>,
    <PERSON><PERSON>tionError, <PERSON>out<PERSON>rror, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, handle_llm_errors, ErrorContext
)

__all__ = [
    # Base classes
    "BaseLLMClient", "CompletionRequest", "ChatRequest", "Message", "MessageRole",
    
    # LLM Clients
    "OpenAIClient", "ClaudeClient", "GeminiClient",
    
    # Prompt Engineering
    "TemplateManager", "PromptTemplate",
    "FewShotPromptBuilder", "Example",
    "PromptChain", "ChainBuilder", "ChainExecutor",
    
    # Utilities
    "setup_logging", "get_logger",
    "MemoryCache", "FileCache", "ResponseCache",
    "APIRateLimiter", "RateLimit",
    "TokenCounter", "Provider",
    
    # Error Handling
    "LLMError", "APIError", "RateLimitError", "AuthenticationError",
    "ValidationError", "TimeoutError", "QuotaExceededError",
    "ErrorHandler", "handle_llm_errors", "ErrorContext",
]
