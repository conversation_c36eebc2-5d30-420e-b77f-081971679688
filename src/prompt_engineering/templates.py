"""
Template Management

Template management system for prompt engineering.
"""

import logging
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from string import Template
import re


logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Prompt template with metadata"""
    name: str
    template: str
    description: str
    variables: List[str]
    category: Optional[str] = None
    examples: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None

    def render(self, **kwargs) -> str:
        """
        Render template with provided variables.

        Args:
            **kwargs: Template variables

        Returns:
            Rendered template

        Raises:
            ValueError: If required variables are missing
        """
        # Check for missing required variables
        missing_vars = set(self.variables) - set(kwargs.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")

        # Use string.Template for safe substitution
        template = Template(self.template)

        try:
            return template.substitute(**kwargs)
        except KeyError as e:
            raise ValueError(f"Template variable not provided: {e}")

    def validate_variables(self, variables: Dict[str, Any]) -> List[str]:
        """
        Validate provided variables against template requirements.

        Args:
            variables: Variables to validate

        Returns:
            List of validation errors
        """
        errors = []

        # Check for missing required variables
        missing_vars = set(self.variables) - set(variables.keys())
        if missing_vars:
            errors.append(f"Missing required variables: {missing_vars}")

        # Check for extra variables
        extra_vars = set(variables.keys()) - set(self.variables)
        if extra_vars:
            errors.append(f"Unexpected variables: {extra_vars}")

        return errors


class TemplateManager:
    """
    Template management system for loading and managing prompt templates.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize template manager.

        Args:
            config_path: Path to template configuration file
        """
        self.templates: Dict[str, PromptTemplate] = {}
        self.categories: Dict[str, List[str]] = {}

        if config_path is None:
            config_path = Path(__file__).parent.parent.parent / "config" / "prompt_templates.yaml"

        self.config_path = Path(config_path)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Load templates if config exists
        if self.config_path.exists():
            self.load_templates()
        else:
            self.logger.warning(f"Template config not found at {config_path}")

    def load_templates(self) -> None:
        """Load templates from configuration file"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            self._parse_config(config)
            self.logger.info(f"Loaded {len(self.templates)} templates from {self.config_path}")

        except Exception as e:
            self.logger.error(f"Error loading templates: {e}")
            raise

    def _parse_config(self, config: Dict[str, Any]) -> None:
        """Parse template configuration"""
        for category, templates in config.items():
            if not isinstance(templates, dict):
                continue

            for template_name, template_data in templates.items():
                if not isinstance(template_data, dict):
                    continue

                # Extract template variables
                template_str = template_data.get('template', '')
                variables = self._extract_variables(template_str)

                # Create template object
                full_name = f"{category}.{template_name}"
                template = PromptTemplate(
                    name=full_name,
                    template=template_str,
                    description=template_data.get('description', ''),
                    variables=variables,
                    category=category,
                    examples=template_data.get('examples'),
                    metadata=template_data.get('metadata')
                )

                self.templates[full_name] = template

                # Update categories
                if category not in self.categories:
                    self.categories[category] = []
                self.categories[category].append(full_name)

    def _extract_variables(self, template: str) -> List[str]:
        """Extract variable names from template string"""
        # Find variables in ${variable} format
        pattern = r'\$\{([^}]+)\}'
        variables = re.findall(pattern, template)

        # Also find variables in $variable format
        pattern2 = r'\$([a-zA-Z_][a-zA-Z0-9_]*)'
        variables2 = re.findall(pattern2, template)

        # Combine and deduplicate
        all_variables = list(set(variables + variables2))
        return sorted(all_variables)

    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """
        Get template by name.

        Args:
            name: Template name (category.template_name)

        Returns:
            PromptTemplate or None if not found
        """
        return self.templates.get(name)

    def list_templates(self, category: Optional[str] = None) -> List[str]:
        """
        List available templates.

        Args:
            category: Filter by category (optional)

        Returns:
            List of template names
        """
        if category:
            return self.categories.get(category, [])
        else:
            return list(self.templates.keys())

    def list_categories(self) -> List[str]:
        """
        List available categories.

        Returns:
            List of category names
        """
        return list(self.categories.keys())

    def add_template(self, template: PromptTemplate) -> None:
        """
        Add a new template.

        Args:
            template: Template to add
        """
        self.templates[template.name] = template

        if template.category:
            if template.category not in self.categories:
                self.categories[template.category] = []
            if template.name not in self.categories[template.category]:
                self.categories[template.category].append(template.name)

        self.logger.info(f"Added template: {template.name}")

    def remove_template(self, name: str) -> bool:
        """
        Remove a template.

        Args:
            name: Template name to remove

        Returns:
            True if template was removed
        """
        if name in self.templates:
            template = self.templates[name]
            del self.templates[name]

            # Remove from category
            if template.category and template.category in self.categories:
                if name in self.categories[template.category]:
                    self.categories[template.category].remove(name)

            self.logger.info(f"Removed template: {name}")
            return True

        return False

    def render_template(self, name: str, **kwargs) -> str:
        """
        Render a template with provided variables.

        Args:
            name: Template name
            **kwargs: Template variables

        Returns:
            Rendered template

        Raises:
            ValueError: If template not found or variables missing
        """
        template = self.get_template(name)
        if not template:
            raise ValueError(f"Template not found: {name}")

        return template.render(**kwargs)

    def validate_template_variables(self, name: str, variables: Dict[str, Any]) -> List[str]:
        """
        Validate variables for a template.

        Args:
            name: Template name
            variables: Variables to validate

        Returns:
            List of validation errors
        """
        template = self.get_template(name)
        if not template:
            return [f"Template not found: {name}"]

        return template.validate_variables(variables)

    def search_templates(self, query: str) -> List[str]:
        """
        Search templates by name or description.

        Args:
            query: Search query

        Returns:
            List of matching template names
        """
        query_lower = query.lower()
        matches = []

        for name, template in self.templates.items():
            if (query_lower in name.lower() or
                query_lower in template.description.lower()):
                matches.append(name)

        return matches