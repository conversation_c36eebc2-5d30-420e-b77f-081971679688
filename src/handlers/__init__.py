"""
Error Handlers Module

Contains error handling utilities and exception classes.
"""

from .error_handler import (
    ErrorType, ErrorSeverity, ErrorInfo,
    LLMError, APIError, RateLimitError, AuthenticationError,
    ValidationError, TimeoutError, QuotaExceededError,
    ErrorHandler, handle_llm_errors, ErrorContext,
    safe_execute, safe_execute_async, global_error_handler
)

__all__ = [
    "ErrorType", "ErrorSeverity", "ErrorInfo",
    "LLMError", "APIError", "RateLimitError", "AuthenticationError",
    "ValidationError", "TimeoutError", "QuotaExceededError",
    "ErrorHandler", "handle_llm_errors", "ErrorContext",
    "safe_execute", "safe_execute_async", "global_error_handler"
]
